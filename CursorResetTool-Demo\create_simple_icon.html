<!DOCTYPE html>
<html>
<head>
    <title>Create Icon for 尚书CAR</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .icon-preview { 
            width: 256px; 
            height: 256px; 
            border: 1px solid #ccc; 
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #1976D2, #2196F3);
            border-radius: 20px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }
        .icon-content {
            color: white;
            text-align: center;
            font-size: 48px;
            font-weight: bold;
        }
        .small-icon {
            width: 64px;
            height: 64px;
            font-size: 12px;
            margin: 10px;
            display: inline-flex;
        }
        .medium-icon {
            width: 128px;
            height: 128px;
            font-size: 24px;
            margin: 10px;
            display: inline-flex;
        }
        button {
            background: #1976D2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565C0;
        }
    </style>
</head>
<body>
    <h1>尚书CAR - Cursor重置工具图标生成器</h1>
    
    <div class="icon-preview" id="mainIcon">
        <div class="icon-content">
            🎯<br>
            <div style="font-size: 24px;">CAR</div>
        </div>
    </div>
    
    <h3>不同尺寸预览：</h3>
    <div class="icon-preview small-icon">
        <div class="icon-content">🎯</div>
    </div>
    <div class="icon-preview medium-icon">
        <div class="icon-content">🎯<br><small>CAR</small></div>
    </div>
    
    <h3>操作说明：</h3>
    <p>1. 右键点击上面的图标</p>
    <p>2. 选择"另存为图片"或截图</p>
    <p>3. 保存为 app_icon.png</p>
    <p>4. 使用在线ICO转换工具转换为ICO格式</p>
    
    <h3>推荐的在线ICO转换工具：</h3>
    <ul>
        <li><a href="https://www.icoconverter.com/" target="_blank">ICO Converter</a></li>
        <li><a href="https://convertio.co/png-ico/" target="_blank">Convertio</a></li>
        <li><a href="https://favicon.io/favicon-converter/" target="_blank">Favicon.io</a></li>
    </ul>
    
    <button onclick="downloadIcon()">生成下载链接</button>
    <button onclick="changeStyle()">切换样式</button>
    
    <script>
        let styleIndex = 0;
        const styles = [
            { bg: 'linear-gradient(135deg, #1976D2, #2196F3)', icon: '🎯', text: 'CAR' },
            { bg: 'linear-gradient(135deg, #4CAF50, #8BC34A)', icon: '🔄', text: 'RST' },
            { bg: 'linear-gradient(135deg, #FF9800, #FFC107)', icon: '⚡', text: 'CAR' },
            { bg: 'linear-gradient(135deg, #9C27B0, #E91E63)', icon: '🛡️', text: 'SEC' }
        ];
        
        function changeStyle() {
            styleIndex = (styleIndex + 1) % styles.length;
            const style = styles[styleIndex];
            const icons = document.querySelectorAll('.icon-preview');
            
            icons.forEach(icon => {
                icon.style.background = style.bg;
                const content = icon.querySelector('.icon-content');
                if (content) {
                    content.innerHTML = style.icon + '<br><div style="font-size: 24px;">' + style.text + '</div>';
                }
            });
        }
        
        function downloadIcon() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 256;
            canvas.height = 256;
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, 256, 256);
            gradient.addColorStop(0, '#1976D2');
            gradient.addColorStop(1, '#2196F3');
            
            // 绘制圆角矩形背景
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, 256, 256, 20);
            ctx.fill();
            
            // 绘制图标文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 48px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🎯', 128, 120);
            
            ctx.font = 'bold 32px Arial';
            ctx.fillText('CAR', 128, 180);
            
            // 创建下载链接
            const link = document.createElement('a');
            link.download = 'app_icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 添加圆角矩形支持
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
            };
        }
    </script>
</body>
</html>
