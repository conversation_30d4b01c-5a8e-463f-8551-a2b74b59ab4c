<Application x:Class="CursorResetTool.Demo.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Cyan" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 自定义颜色 -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#1976D2"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#03DAC6"/>
            <SolidColorBrush x:Key="AccentBrush" Color="#FF4081"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="TextPrimaryBrush" Color="#212121"/>
            <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>
            
            <!-- 字体 -->
            <FontFamily x:Key="PrimaryFont">Microsoft YaHei UI, Segoe UI, Arial</FontFamily>
            <FontFamily x:Key="MonospaceFont">Consolas, Courier New</FontFamily>
            
            <!-- 文本样式 -->
            <Style x:Key="TitleTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            </Style>
            
            <Style x:Key="SubtitleTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            </Style>
            
            <Style x:Key="BodyTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                <Setter Property="TextWrapping" Value="Wrap"/>
            </Style>
            
            <!-- 卡片样式 -->
            <Style x:Key="CardStyle" TargetType="Border">
                <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
                <Setter Property="CornerRadius" Value="8"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                    </Setter.Value>
                </Setter>
            </Style>
            
            <!-- 按钮样式 -->
            <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Height" Value="40"/>
                <Setter Property="Padding" Value="24,8"/>
                <Setter Property="Margin" Value="4"/>
            </Style>
            
            <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Height" Value="40"/>
                <Setter Property="Padding" Value="24,8"/>
                <Setter Property="Margin" Value="4"/>
            </Style>
            
            <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Height" Value="40"/>
                <Setter Property="Padding" Value="24,8"/>
                <Setter Property="Margin" Value="4"/>
            </Style>
            
            <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Height" Value="40"/>
                <Setter Property="Padding" Value="24,8"/>
                <Setter Property="Margin" Value="4"/>
            </Style>
            
        </ResourceDictionary>
    </Application.Resources>
</Application>
