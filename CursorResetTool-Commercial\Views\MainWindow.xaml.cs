using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using CursorResetTool.Commercial.Services;

namespace CursorResetTool.Commercial.Views
{
    public partial class MainWindow : Window
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IUserService _userService;
        private readonly ILicenseService _licenseService;
        private readonly ICursorResetService _cursorResetService;
        private readonly IMachineCodeService _machineCodeService;

        public MainWindow()
        {
            InitializeComponent();
            
            // 从App获取服务提供者
            _serviceProvider = ((App)Application.Current).Host?.Services ?? 
                throw new InvalidOperationException("Service provider not available");

            _userService = _serviceProvider.GetRequiredService<IUserService>();
            _licenseService = _serviceProvider.GetRequiredService<ILicenseService>();
            _cursorResetService = _serviceProvider.GetRequiredService<ICursorResetService>();
            _machineCodeService = _serviceProvider.GetRequiredService<IMachineCodeService>();

            Loaded += MainWindow_Loaded;
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                await InitializeWindowAsync();
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to initialize main window");
                UpdateStatus($"初始化失败：{ex.Message}");
            }
        }

        private async Task InitializeWindowAsync()
        {
            // 更新用户信息
            var currentUser = _userService.GetCurrentUser();
            var currentLicense = _licenseService.GetCurrentLicense();

            if (currentUser != null)
            {
                UserInfoText.Text = $"用户: {currentUser.Username} | 许可证: {GetLicenseTypeText(currentUser.MembershipLevel)}";
            }

            // 更新许可证信息
            if (currentLicense != null)
            {
                LicenseTypeText.Text = $"类型: {GetLicenseTypeText(currentLicense.LicenseType)}";
                
                if (currentLicense.ExpiryDate.HasValue)
                {
                    var remainingDays = (currentLicense.ExpiryDate.Value - DateTime.Now).Days;
                    LicenseExpiryText.Text = remainingDays > 0 ? $"剩余: {remainingDays} 天" : "已过期";
                }
                else
                {
                    LicenseExpiryText.Text = "永久许可证";
                }
            }

            // 更新系统信息
            await UpdateSystemInfoAsync();

            UpdateStatus("就绪 - 请选择要执行的操作");
        }

        private async Task UpdateSystemInfoAsync()
        {
            try
            {
                OSText.Text = Environment.OSVersion.ToString();
                ArchText.Text = Environment.Is64BitOperatingSystem ? "x64" : "x86";
                
                var machineCode = await Task.Run(() => _machineCodeService.GenerateMachineCode());
                MachineCodeText.Text = machineCode.Length > 20 ? machineCode.Substring(0, 20) + "..." : machineCode;
            }
            catch (Exception ex)
            {
                Serilog.Log.Warning(ex, "Failed to update system info");
            }
        }

        private string GetLicenseTypeText(Models.MembershipLevel level)
        {
            return level switch
            {
                Models.MembershipLevel.Trial => "试用版",
                Models.MembershipLevel.Personal => "个人版",
                Models.MembershipLevel.Professional => "专业版",
                Models.MembershipLevel.Enterprise => "企业版",
                _ => "未知"
            };
        }

        private string GetLicenseTypeText(Models.LicenseType type)
        {
            return type switch
            {
                Models.LicenseType.Trial => "试用版",
                Models.LicenseType.Personal => "个人版",
                Models.LicenseType.Professional => "专业版",
                Models.LicenseType.Enterprise => "企业版",
                _ => "未知"
            };
        }

        private async void ScanButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("正在扫描 Cursor 数据...");
                OperationProgress.Visibility = Visibility.Visible;
                OperationProgress.IsIndeterminate = true;
                ScanButton.IsEnabled = false;

                var scanResult = await _cursorResetService.ScanCursorDataAsync();

                OperationProgress.Visibility = Visibility.Collapsed;
                OperationTitle.Text = "扫描结果";

                // 清空内容面板并显示扫描结果
                ContentPanel.Children.Clear();
                
                var resultText = $@"扫描完成！

发现的项目：
• 文件和目录: {scanResult.Files.Count} 个
• 注册表项: {scanResult.RegistryItems.Count} 个
• 总大小: {FormatFileSize(scanResult.TotalSize)}

详细信息：
{string.Join("\n", scanResult.Files.Take(10).Select(f => $"• {f.Path} ({f.Type})"))}
{(scanResult.Files.Count > 10 ? $"\n... 还有 {scanResult.Files.Count - 10} 个文件" : "")}";

                var resultBlock = new TextBlock
                {
                    Text = resultText,
                    Style = (Style)FindResource("BodyTextStyle"),
                    TextWrapping = TextWrapping.Wrap,
                    FontFamily = (System.Windows.Media.FontFamily)FindResource("MonospaceFont")
                };

                ContentPanel.Children.Add(resultBlock);
                UpdateStatus($"扫描完成 - 发现 {scanResult.TotalItems} 个项目");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Scan operation failed");
                UpdateStatus($"扫描失败：{ex.Message}");
            }
            finally
            {
                OperationProgress.Visibility = Visibility.Collapsed;
                ScanButton.IsEnabled = true;
            }
        }

        private async void BackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("正在备份 Cursor 数据...");
                OperationProgress.Visibility = Visibility.Visible;
                OperationProgress.IsIndeterminate = false;
                BackupButton.IsEnabled = false;

                var progress = new Progress<int>(value => OperationProgress.Value = value);
                var (success, backupPath, message) = await _cursorResetService.BackupCursorDataAsync(progress);

                OperationProgress.Visibility = Visibility.Collapsed;

                if (success)
                {
                    OperationTitle.Text = "备份完成";
                    ContentPanel.Children.Clear();
                    
                    var resultText = $@"备份成功！

备份文件: {System.IO.Path.GetFileName(backupPath)}
保存位置: {System.IO.Path.GetDirectoryName(backupPath)}

{message}

建议：
• 请妥善保管备份文件
• 清理前请确认备份完整
• 如需恢复，请使用恢复功能";

                    var resultBlock = new TextBlock
                    {
                        Text = resultText,
                        Style = (Style)FindResource("BodyTextStyle"),
                        TextWrapping = TextWrapping.Wrap
                    };

                    ContentPanel.Children.Add(resultBlock);
                    UpdateStatus("备份完成");
                }
                else
                {
                    UpdateStatus($"备份失败：{message}");
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Backup operation failed");
                UpdateStatus($"备份失败：{ex.Message}");
            }
            finally
            {
                OperationProgress.Visibility = Visibility.Collapsed;
                BackupButton.IsEnabled = true;
            }
        }

        private async void CleanButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "确定要清理 Cursor 数据吗？\n\n此操作将：\n• 删除所有 Cursor 相关文件\n• 清理注册表项\n• 关闭相关进程\n\n建议在清理前先进行备份！",
                    "确认清理",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                    return;

                UpdateStatus("正在清理 Cursor 数据...");
                OperationProgress.Visibility = Visibility.Visible;
                OperationProgress.IsIndeterminate = false;
                CleanButton.IsEnabled = false;

                var progress = new Progress<int>(value => OperationProgress.Value = value);
                var (success, message) = await _cursorResetService.CleanCursorDataAsync(progress);

                OperationProgress.Visibility = Visibility.Collapsed;

                if (success)
                {
                    OperationTitle.Text = "清理完成";
                    ContentPanel.Children.Clear();
                    
                    var resultText = $@"清理成功！

{message}

建议：
• 重启计算机以确保清理完全生效
• 如需重新安装 Cursor，请从官网下载
• 如遇问题，可使用备份文件恢复

注意：清理操作已完成，建议立即重启系统。";

                    var resultBlock = new TextBlock
                    {
                        Text = resultText,
                        Style = (Style)FindResource("BodyTextStyle"),
                        TextWrapping = TextWrapping.Wrap,
                        Foreground = (System.Windows.Media.Brush)FindResource("SuccessBrush")
                    };

                    ContentPanel.Children.Add(resultBlock);
                    UpdateStatus("清理完成 - 建议重启系统");
                }
                else
                {
                    UpdateStatus($"清理失败：{message}");
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Clean operation failed");
                UpdateStatus($"清理失败：{ex.Message}");
            }
            finally
            {
                OperationProgress.Visibility = Visibility.Collapsed;
                CleanButton.IsEnabled = true;
            }
        }

        private async void RestoreButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取备份列表
                var backups = await _cursorResetService.GetBackupListAsync();
                
                if (backups.Count == 0)
                {
                    MessageBox.Show("未找到备份文件！", "恢复失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 这里应该显示备份选择对话框，简化处理选择最新的备份
                var latestBackup = backups.OrderByDescending(b => b.CreateTime).First();
                
                var result = MessageBox.Show(
                    $"确定要恢复备份吗？\n\n备份文件: {latestBackup.FileName}\n创建时间: {latestBackup.CreateTime:yyyy-MM-dd HH:mm:ss}\n\n此操作将覆盖当前的 Cursor 数据！",
                    "确认恢复",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes)
                    return;

                UpdateStatus("正在恢复 Cursor 数据...");
                OperationProgress.Visibility = Visibility.Visible;
                OperationProgress.IsIndeterminate = false;
                RestoreButton.IsEnabled = false;

                var progress = new Progress<int>(value => OperationProgress.Value = value);
                var (success, message) = await _cursorResetService.RestoreCursorDataAsync(latestBackup.FilePath, progress);

                OperationProgress.Visibility = Visibility.Collapsed;

                if (success)
                {
                    UpdateStatus("恢复完成");
                    MessageBox.Show("数据恢复成功！", "恢复完成", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    UpdateStatus($"恢复失败：{message}");
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Restore operation failed");
                UpdateStatus($"恢复失败：{ex.Message}");
            }
            finally
            {
                OperationProgress.Visibility = Visibility.Collapsed;
                RestoreButton.IsEnabled = true;
            }
        }

        private void ViewLogsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var logsPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                if (System.IO.Directory.Exists(logsPath))
                {
                    System.Diagnostics.Process.Start("explorer.exe", logsPath);
                }
                else
                {
                    MessageBox.Show("日志目录不存在！", "查看日志", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to open logs directory");
                UpdateStatus($"打开日志目录失败：{ex.Message}");
            }
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "确定要退出登录吗？",
                    "退出登录",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // 清除当前用户和许可证
                    _userService.SetCurrentUser(null);
                    _licenseService.SetCurrentLicense(null);

                    // 显示登录窗口
                    var loginWindow = _serviceProvider.GetRequiredService<LoginWindow>();
                    loginWindow.Show();

                    // 关闭主窗口
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Logout failed");
                UpdateStatus($"退出登录失败：{ex.Message}");
            }
        }

        private void UpdateStatus(string message)
        {
            StatusText.Text = message;
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
