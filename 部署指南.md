# Cursor Reset Tool Pro - 部署指南

## 🎯 项目概述

这是一个带有网络注册验证功能的 Cursor 重置工具，展示了完整的软件授权系统实现。

### 🔧 核心功能

1. **网络激活** - 通过服务器验证许可证
2. **离线激活** - 本地验证机制
3. **试用期管理** - 7天免费试用
4. **许可证类型** - 试用版、个人版、专业版、企业版
5. **机器绑定** - 基于硬件指纹的设备绑定
6. **加密存储** - 许可证信息加密保存

## 📁 项目结构

```
CursorReset/
├── Models/
│   └── LicenseInfo.cs          # 许可证数据模型
├── Services/
│   ├── CryptographyService.cs  # 加密和硬件指纹
│   ├── LicenseService.cs       # 许可证管理
│   └── ActivationService.cs    # 激活服务
├── UI/
│   └── LicenseManager.cs       # 许可证界面
├── Server/
│   └── MockLicenseServer.cs    # 模拟许可证服务器
├── Program.cs                  # 主程序
└── 其他核心文件...
```

## 🚀 快速开始

### 1. 编译项目

```bash
# 安装依赖
dotnet restore

# 编译项目
dotnet build

# 运行程序
dotnet run
```

### 2. 测试许可证

程序内置了以下测试许可证：

- **DEMO-1234-5678-9ABC** - 专业版，3个激活
- **TEST-ABCD-EFGH-IJKL** - 个人版，1个激活  
- **ENTERPRISE-2024-FULL** - 企业版，100个激活

### 3. 激活流程

1. 运行程序
2. 选择"许可证管理"
3. 选择"激活许可证"
4. 输入测试许可证密钥
5. 输入邮箱地址
6. 等待激活完成

## 🔐 安全机制

### 硬件指纹生成

```csharp
public static string GenerateMachineId()
{
    var machineInfo = new StringBuilder();
    machineInfo.Append(GetCpuId());        // CPU ID
    machineInfo.Append(GetMotherboardId()); // 主板序列号
    machineInfo.Append(GetMacAddress());    // MAC 地址
    machineInfo.Append(GetDiskId());        // 硬盘序列号
    
    using var sha256 = SHA256.Create();
    var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineInfo.ToString()));
    return Convert.ToBase64String(hash).Substring(0, 32);
}
```

### 许可证签名验证

```csharp
public static bool VerifySignature(string data, string signature, string publicKey)
{
    var expectedSignature = GenerateSignature(data, publicKey);
    return expectedSignature == signature;
}
```

### 加密存储

- 许可证信息使用 AES 加密存储
- 同时保存到注册表和文件系统
- 试用信息隐藏存储，防止篡改

## 🌐 网络架构

### 模拟服务器

当前使用 `MockLicenseServer` 模拟真实的许可证服务器：

```csharp
public static ActivationResponse ProcessActivation(ActivationRequest request)
{
    // 验证许可证密钥
    // 检查激活次数限制
    // 生成签名
    // 返回激活结果
}
```

### 真实部署

在生产环境中，需要：

1. **Web API 服务器** - 处理激活请求
2. **数据库** - 存储许可证信息
3. **HTTPS 加密** - 保护网络传输
4. **负载均衡** - 处理高并发请求

## 📦 打包发布

### 单文件发布

```bash
# Windows x64
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true

# Windows x86
dotnet publish -c Release -r win-x86 --self-contained true -p:PublishSingleFile=true
```

### 安装包制作

可以使用以下工具制作安装包：

1. **Inno Setup** - 免费的 Windows 安装包制作工具
2. **WiX Toolset** - Microsoft 官方安装包工具
3. **Advanced Installer** - 商业安装包制作工具

## 🔧 配置选项

### 许可证服务器配置

```csharp
// Services/ActivationService.cs
private static readonly string ActivationServerUrl = "https://api.cursorreset.com/activate";
private static readonly string ValidationServerUrl = "https://api.cursorreset.com/validate";
```

### 试用期配置

```csharp
// Models/LicenseInfo.cs
public int TrialDays { get; set; } = 7; // 试用天数
```

### 加密密钥配置

```csharp
// Services/LicenseService.cs
private static readonly string EncryptionKey = "CursorResetTool2024SecretKey";
```

## 🛡️ 安全建议

### 1. 密钥管理

- 使用强随机密钥
- 定期轮换加密密钥
- 私钥安全存储

### 2. 网络安全

- 使用 HTTPS 协议
- 实施 API 限流
- 添加请求签名验证

### 3. 客户端保护

- 代码混淆
- 反调试保护
- 完整性检查

## 📊 监控和分析

### 激活统计

- 激活成功率
- 地理分布
- 版本使用情况

### 异常监控

- 激活失败原因
- 网络连接问题
- 许可证滥用检测

## 🔄 更新机制

### 自动更新

```csharp
public class UpdateService
{
    public static async Task<bool> CheckForUpdatesAsync()
    {
        // 检查服务器上的最新版本
        // 下载更新包
        // 应用更新
    }
}
```

### 许可证迁移

- 支持许可证在新版本间迁移
- 保持激活状态
- 升级许可证类型

## 📞 技术支持

### 常见问题

1. **激活失败** - 检查网络连接和许可证有效性
2. **试用期过期** - 购买正式许可证
3. **机器更换** - 联系技术支持重置激活

### 日志收集

程序会在以下位置生成日志：

- `%APPDATA%\CursorResetTool\logs\`
- Windows 事件日志

## ⚖️ 法律声明

- 此项目仅用于学习和演示目的
- 请遵守相关软件的使用条款
- 不得用于非法用途

---

**注意：这是一个教育项目，展示了软件授权系统的实现方法。在实际商业使用中，请确保遵守相关法律法规。**
