# 尚书_CAR_cursor重置系统 - 商用版项目架构说明

## 🎯 项目概述

**尚书_CAR_cursor重置系统** 是一个基于 WPF 的现代化桌面应用程序，采用 Material Design 设计风格，具备完整的用户管理、许可证管理和一机一码防盗版机制。

### 核心特性
- 🎨 **现代化UI** - Material Design + Fluent Design 风格
- 👥 **完整用户系统** - 注册、登录、会员管理
- 🔐 **企业级许可证** - 网络验证、硬件绑定、防盗版
- 🛡️ **一机一码授权** - 基于硬件指纹的设备绑定
- 🌐 **多语言支持** - 中英文界面切换
- 🔄 **自动更新** - 在线更新检查和安装
- 📊 **完整日志** - 多级别日志记录和监控

## 🏗️ 项目架构

### 技术栈
- **.NET 8.0** - 最新的 .NET 框架
- **WPF** - Windows Presentation Foundation
- **MVVM** - Model-View-ViewModel 架构模式
- **Material Design** - MaterialDesignInXamlToolkit
- **Modern WPF** - ModernWpfUI
- **依赖注入** - Microsoft.Extensions.DependencyInjection
- **日志记录** - Serilog
- **加密安全** - BCrypt, AES, RSA

### 项目结构
```
CursorResetTool-Commercial/
├── Models/                     # 数据模型
│   ├── User.cs                # 用户模型
│   └── License.cs             # 许可证模型
├── Services/                   # 业务服务
│   ├── ISecurityService.cs    # 安全服务
│   ├── IMachineCodeService.cs # 机器码服务
│   ├── IUserService.cs        # 用户服务
│   ├── ILicenseService.cs     # 许可证服务
│   ├── INetworkService.cs     # 网络服务
│   ├── ICursorResetService.cs # Cursor重置服务
│   ├── ILocalizationService.cs # 本地化服务
│   └── IUpdateService.cs      # 更新服务
├── Views/                      # 视图界面
│   ├── SplashWindow.xaml      # 启动画面
│   ├── LoginWindow.xaml       # 登录窗口
│   ├── RegisterWindow.xaml    # 注册窗口
│   ├── MainWindow.xaml        # 主窗口
│   └── SettingsWindow.xaml    # 设置窗口
├── ViewModels/                 # 视图模型
│   ├── SplashViewModel.cs     # 启动画面VM
│   ├── LoginViewModel.cs      # 登录VM
│   ├── RegisterViewModel.cs   # 注册VM
│   ├── MainViewModel.cs       # 主窗口VM
│   └── SettingsViewModel.cs   # 设置VM
├── Controls/                   # 自定义控件
├── Converters/                 # 值转换器
├── Styles/                     # 样式资源
│   └── CustomStyles.xaml     # 自定义样式
├── Resources/                  # 资源文件
│   ├── Images/                # 图片资源
│   └── Icons/                 # 图标资源
├── Localization/              # 本地化资源
├── App.xaml                   # 应用程序入口
├── App.xaml.cs               # 应用程序逻辑
└── app.manifest              # 应用程序清单
```

## 🔧 核心服务详解

### 1. 安全服务 (ISecurityService)
**功能**：
- 管理员权限检查
- 密码哈希和验证 (BCrypt)
- AES 加密/解密
- RSA 数字签名
- 随机字符串生成

**关键方法**：
```csharp
bool IsRunAsAdministrator()
string HashPassword(string password, out string salt)
bool VerifyPassword(string password, string hash, string salt)
string EncryptAES(string plainText, string key)
string DecryptAES(string cipherText, string key)
```

### 2. 机器码服务 (IMachineCodeService)
**功能**：
- 生成唯一机器码
- 硬件指纹采集
- 设备验证

**硬件信息采集**：
- CPU ID 和型号
- 主板序列号
- 硬盘序列号
- 内存信息
- 网卡 MAC 地址

**防盗版机制**：
```csharp
string GenerateMachineCode()           // 生成机器码
string GenerateHardwareFingerprint()  // 生成硬件指纹
bool ValidateMachineCode(string code)  // 验证机器码
```

### 3. 用户服务 (IUserService)
**功能**：
- 用户注册和登录
- 密码重置
- 用户信息管理
- 本地数据存储

**会员等级**：
- Trial (试用版)
- Personal (个人版)
- Professional (专业版)
- Enterprise (企业版)

### 4. 许可证服务 (ILicenseService)
**功能**：
- 在线/离线许可证激活
- 许可证验证和管理
- 设备绑定和授权
- 试用许可证生成

**一机一码实现**：
```csharp
// 许可证与机器码绑定
license.MachineCode = machineCodeService.GenerateMachineCode();
license.HardwareFingerprint = machineCodeService.GenerateHardwareFingerprint();

// 验证设备授权
bool IsDeviceAuthorized() => 
    currentLicense.MachineCode == machineCodeService.GenerateMachineCode();
```

### 5. 网络服务 (INetworkService)
**功能**：
- 用户注册/登录 API
- 许可证激活/验证 API
- 更新检查和下载
- 使用统计上报

**API 端点**：
- `/users/register` - 用户注册
- `/users/login` - 用户登录
- `/licenses/activate` - 许可证激活
- `/licenses/validate` - 许可证验证
- `/updates/check` - 检查更新

### 6. Cursor重置服务 (ICursorResetService)
**功能**：
- 扫描 Cursor 相关数据
- 安全备份和恢复
- 彻底清理 Cursor 数据
- 进程管理

**清理范围**：
- 应用数据目录
- 本地数据目录
- 注册表项
- 快捷方式
- 临时文件

## 🎨 UI 设计特性

### Material Design 风格
- **卡片式布局** - 现代化的卡片设计
- **浮动操作按钮** - FAB 设计模式
- **材质动画** - 流畅的过渡动画
- **阴影效果** - 层次感的视觉设计
- **色彩系统** - 一致的色彩规范

### 响应式设计
- 支持不同分辨率
- 自适应布局
- 触摸友好的控件大小
- 高 DPI 支持

### 自定义样式
```xml
<!-- 主要按钮样式 -->
<Style x:Key="PrimaryButtonStyle" TargetType="Button">
    <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
    <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="20"/>
</Style>

<!-- 卡片样式 -->
<Style x:Key="CardStyle" TargetType="Border">
    <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
    <Setter Property="CornerRadius" Value="8"/>
    <Setter Property="Effect">
        <Setter.Value>
            <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2"/>
        </Setter.Value>
    </Setter>
</Style>
```

## 🔐 安全机制

### 1. 一机一码防盗版
**实现原理**：
1. 采集硬件信息生成唯一机器码
2. 许可证激活时绑定机器码
3. 每次启动验证机器码匹配
4. 硬件变更时需要重新授权

**硬件指纹算法**：
```csharp
var components = new List<string>
{
    GetCpuInfo(),           // CPU 信息
    GetMotherboardInfo(),   // 主板信息
    GetHardDiskInfo(),      // 硬盘信息
    GetMemoryInfo(),        // 内存信息
    GetNetworkAdapterInfo() // 网卡信息
};

var combinedInfo = string.Join("|", components);
var hash = SHA256.ComputeHash(Encoding.UTF8.GetBytes(combinedInfo));
var machineCode = Convert.ToBase64String(hash);
```

### 2. 许可证加密存储
- **AES-256 加密** - 本地许可证文件加密
- **数字签名** - RSA 签名验证许可证完整性
- **双重存储** - 主文件 + 隐藏备份文件
- **防篡改** - 文件属性保护

### 3. 网络验证
- **HTTPS 通信** - 安全的网络传输
- **令牌认证** - API 访问控制
- **设备指纹** - 多维度设备识别
- **实时验证** - 定期在线验证

## 🌐 多语言支持

### 支持语言
- 🇨🇳 简体中文 (zh-CN)
- 🇺🇸 英语 (en-US)
- 🇹🇼 繁体中文 (zh-TW)
- 🇯🇵 日语 (ja-JP)
- 🇰🇷 韩语 (ko-KR)

### 本地化实现
```csharp
public interface ILocalizationService
{
    string GetString(string key);
    string GetString(string key, params object[] args);
    void SetLanguage(string languageCode);
    List<LanguageInfo> GetSupportedLanguages();
}
```

## 🔄 自动更新机制

### 更新流程
1. **检查更新** - 定期检查服务器版本
2. **下载更新** - 后台下载更新包
3. **验证完整性** - 校验文件哈希
4. **安装更新** - 自动替换程序文件
5. **重启应用** - 启动新版本

### 更新脚本
```batch
@echo off
echo 正在更新尚书CAR系统...
timeout /t 3 /nobreak >nul

:wait_loop
tasklist /fi "imagename=CursorResetTool.exe" | find /i "CursorResetTool.exe" >nul
if not errorlevel 1 (
    timeout /t 1 /nobreak >nul
    goto wait_loop
)

copy "update.exe" "CursorResetTool.exe" >nul
start "" "CursorResetTool.exe"
del "%~f0" >nul
```

## 📊 日志和监控

### 日志级别
- **Debug** - 调试信息
- **Information** - 一般信息
- **Warning** - 警告信息
- **Error** - 错误信息
- **Critical** - 严重错误

### 日志配置
```csharp
Log.Logger = new LoggerConfiguration()
    .WriteTo.File("logs/app-.log", rollingInterval: RollingInterval.Day)
    .WriteTo.Console()
    .CreateLogger();
```

### 监控指标
- 用户操作记录
- 性能指标统计
- 错误率监控
- 许可证使用情况

## 🚀 部署和配置

### 系统要求
- **操作系统**: Windows 10/11 (64位推荐)
- **框架**: .NET 8.0 Runtime
- **权限**: 管理员权限
- **网络**: 激活时需要网络连接

### 发布配置
```xml
<PropertyGroup>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishTrimmed>false</PublishTrimmed>
</PropertyGroup>
```

### 配置文件
```json
{
  "ApiBaseUrl": "https://api.shangshu-car.com/v1",
  "AutoUpdate": true,
  "LogLevel": "Information",
  "Language": "zh-CN",
  "Theme": "Light"
}
```

## 📈 扩展性设计

### 插件架构
- 模块化设计
- 依赖注入容器
- 接口驱动开发
- 热插拔支持

### API 扩展
- RESTful API 设计
- 版本控制
- 向后兼容
- 文档化接口

### 数据库支持
- 本地 SQLite 存储
- 云端数据同步
- 数据迁移机制
- 备份和恢复

## 🎯 商业价值

### 技术优势
- **现代化架构** - 采用最新技术栈
- **企业级安全** - 完整的安全机制
- **用户体验** - 专业的UI设计
- **可维护性** - 清晰的代码结构

### 商业特性
- **许可证管理** - 灵活的授权模式
- **防盗版保护** - 有效的版权保护
- **用户管理** - 完整的用户体系
- **数据分析** - 使用情况统计

### 市场定位
- **专业工具** - 面向专业用户
- **企业级** - 满足企业需求
- **可扩展** - 支持定制开发
- **国际化** - 多语言支持

---

这个商用版本展示了从简单工具到企业级软件的完整转变，为现代软件开发提供了优秀的参考案例。通过完整的架构设计、安全机制和用户体验，打造了一个具备商业价值的专业软件产品。
