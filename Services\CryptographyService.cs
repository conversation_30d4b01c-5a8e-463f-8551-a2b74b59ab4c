using System;
using System.Security.Cryptography;
using System.Text;

namespace CursorReset.Services
{
    public class CryptographyService
    {
        private static readonly string PrivateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB"; // 示例密钥
        private static readonly string PublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1SU1L7VLPHCgQBDoS9u"; // 示例密钥

        public static string GenerateMachineId()
        {
            try
            {
                var machineInfo = new StringBuilder();
                
                // CPU ID
                var cpuId = GetCpuId();
                machineInfo.Append(cpuId);
                
                // 主板序列号
                var motherboardId = GetMotherboardId();
                machineInfo.Append(motherboardId);
                
                // MAC 地址
                var macAddress = GetMacAddress();
                machineInfo.Append(macAddress);
                
                // 硬盘序列号
                var diskId = GetDiskId();
                machineInfo.Append(diskId);
                
                // 生成哈希
                using var sha256 = SHA256.Create();
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineInfo.ToString()));
                return Convert.ToBase64String(hash).Substring(0, 32);
            }
            catch
            {
                // 如果获取硬件信息失败，使用备用方法
                var fallback = Environment.MachineName + Environment.UserName + Environment.OSVersion.ToString();
                using var sha256 = SHA256.Create();
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(fallback));
                return Convert.ToBase64String(hash).Substring(0, 32);
            }
        }

        private static string GetCpuId()
        {
            try
            {
                using var searcher = new System.Management.ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor");
                foreach (var obj in searcher.Get())
                {
                    return obj["ProcessorId"]?.ToString() ?? "";
                }
            }
            catch { }
            return Environment.ProcessorCount.ToString();
        }

        private static string GetMotherboardId()
        {
            try
            {
                using var searcher = new System.Management.ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard");
                foreach (var obj in searcher.Get())
                {
                    return obj["SerialNumber"]?.ToString() ?? "";
                }
            }
            catch { }
            return "UNKNOWN";
        }

        private static string GetMacAddress()
        {
            try
            {
                var networkInterfaces = System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces();
                foreach (var ni in networkInterfaces)
                {
                    if (ni.NetworkInterfaceType == System.Net.NetworkInformation.NetworkInterfaceType.Ethernet ||
                        ni.NetworkInterfaceType == System.Net.NetworkInformation.NetworkInterfaceType.Wireless80211)
                    {
                        return ni.GetPhysicalAddress().ToString();
                    }
                }
            }
            catch { }
            return "000000000000";
        }

        private static string GetDiskId()
        {
            try
            {
                using var searcher = new System.Management.ManagementObjectSearcher("SELECT SerialNumber FROM Win32_DiskDrive WHERE MediaType='Fixed hard disk media'");
                foreach (var obj in searcher.Get())
                {
                    return obj["SerialNumber"]?.ToString()?.Trim() ?? "";
                }
            }
            catch { }
            return "UNKNOWN";
        }

        public static string EncryptString(string plainText, string key)
        {
            try
            {
                using var aes = Aes.Create();
                aes.Key = SHA256.HashData(Encoding.UTF8.GetBytes(key))[..32];
                aes.IV = new byte[16]; // 简化版本，实际应用中应使用随机IV
                
                using var encryptor = aes.CreateEncryptor();
                var plainBytes = Encoding.UTF8.GetBytes(plainText);
                var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
                
                return Convert.ToBase64String(encryptedBytes);
            }
            catch
            {
                return plainText; // 加密失败时返回原文
            }
        }

        public static string DecryptString(string cipherText, string key)
        {
            try
            {
                using var aes = Aes.Create();
                aes.Key = SHA256.HashData(Encoding.UTF8.GetBytes(key))[..32];
                aes.IV = new byte[16]; // 简化版本，实际应用中应使用随机IV
                
                using var decryptor = aes.CreateDecryptor();
                var cipherBytes = Convert.FromBase64String(cipherText);
                var decryptedBytes = decryptor.TransformFinalBlock(cipherBytes, 0, cipherBytes.Length);
                
                return Encoding.UTF8.GetString(decryptedBytes);
            }
            catch
            {
                return string.Empty; // 解密失败时返回空字符串
            }
        }

        public static string GenerateSignature(string data, string privateKey)
        {
            try
            {
                using var sha256 = SHA256.Create();
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(data + privateKey));
                return Convert.ToBase64String(hash);
            }
            catch
            {
                return string.Empty;
            }
        }

        public static bool VerifySignature(string data, string signature, string publicKey)
        {
            try
            {
                var expectedSignature = GenerateSignature(data, publicKey);
                return expectedSignature == signature;
            }
            catch
            {
                return false;
            }
        }

        public static string GenerateLicenseKey()
        {
            var random = new Random();
            var parts = new string[4];
            
            for (int i = 0; i < 4; i++)
            {
                var part = "";
                for (int j = 0; j < 4; j++)
                {
                    part += random.Next(0, 36).ToString("X");
                }
                parts[i] = part;
            }
            
            return string.Join("-", parts);
        }
    }
}
