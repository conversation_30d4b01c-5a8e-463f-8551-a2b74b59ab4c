完美，在保留现有版本的基础上，新增加一个文件夹来创建新的版本，要求：有好看的接近商用的好看UI，有会员注册、管理和网络验证许可的功能，并实现一机一码授权机制防止复制传播


在保留现有的控制台版本 Cursor Reset Tool Pro 的基础上，创建一个全新的商用级 WPF 桌面应用程序版本，具体要求如下：

**项目结构要求：**
- 在新文件夹 `CursorResetTool-Commercial` 中创建独立项目
- 保持原有控制台版本完全不变
- 使用 .NET 8.0 WPF 框架开发

**UI设计要求：**
- 采用现代化 Material Design 或 Fluent Design 风格
- 使用专业UI库（如 MaterialDesignInXamlToolkit 或 ModernWpfUI）
- 实现响应式布局，支持不同分辨率
- 包含启动画面、主界面、设置界面等多个窗口
- 支持中英文界面切换
- 具备商用软件级别的视觉效果和用户体验

**会员系统功能：**
- 用户注册功能（用户名、邮箱、密码）
- 用户登录验证
- 会员等级管理（试用版、个人版、专业版、企业版）
- 用户资料管理和修改
- 密码重置功能

**许可证管理功能：**
- 在线许可证激活和验证
- 离线许可证激活备用方案
- 许可证到期提醒和续费
- 多设备许可证管理
- 许可证状态实时显示

**一机一码防盗版机制：**
- 基于硬件指纹生成唯一机器码（CPU ID、主板序列号、硬盘序列号等）
- 许可证与机器码绑定
- 验证设备授权
- 防止复制和传播

**其他要求：**
- 实现自动更新功能
- 提供详细的使用文档和说明
- 确保代码质量和可维护性
- 保持与现有控制台版本的功能一致性和兼容性