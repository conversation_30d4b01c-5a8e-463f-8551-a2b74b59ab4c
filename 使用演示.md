# Cursor Reset Tool Pro - 使用演示

## 🎯 功能演示

这个增强版的 Cursor Reset Tool 现在包含了完整的网络注册验证系统。以下是详细的使用演示：

## 🚀 启动程序

### 1. 管理员权限检查

程序启动时会自动检查管理员权限：

```
=== Cursor Reset Tool Pro (Licensed Version) ===
警告：此工具仅用于学习目的！
Warning: This tool is for educational purposes only!

✅ 检测到管理员权限
```

### 2. 许可证状态检查

程序会自动检查许可证状态：

```
🔍 检查许可证状态...
❌ 许可证无效或试用期已过期

请选择：
1. 激活许可证
2. 离线激活
3. 申请试用
4. 退出程序
```

## 🔐 许可证管理

### 试用期体验

首次运行时，程序会显示试用信息：

```
=== Cursor Reset Tool Pro ===
🔓 试用版 | 剩余: 7 天

请选择操作：
1. 扫描 Cursor 相关文件和注册表
2. 备份 Cursor 数据
3. 清理 Cursor 数据 (谨慎操作!)
4. 恢复备份
5. 许可证管理
6. 退出
```

### 许可证激活演示

选择"许可证管理"进入激活界面：

```
=== Cursor Reset Tool - 许可证管理 ===

🔓 许可证状态: 未激活
📅 首次运行: 2024-01-15 10:30:00
🔢 运行次数: 1
⏳ 试用期: 7 天
⏰ 剩余天数: 7 天
💻 机器ID: ABC123DEF456GHI789JKL012

1. 激活许可证
2. 离线激活
3. 申请试用
4. 查看测试许可证
5. 返回主菜单
```

### 查看测试许可证

选择"查看测试许可证"可以看到可用的测试密钥：

```
=== 测试许可证密钥 ===

💡 以下是可用于测试的许可证密钥：

许可证密钥: DEMO-1234-5678-9ABC
类型: 专业版
最大激活数: 3
当前激活数: 0
到期日期: 2025-01-15
状态: 有效

许可证密钥: TEST-ABCD-EFGH-IJKL
类型: 个人版
最大激活数: 1
当前激活数: 0
到期日期: 2024-07-15
状态: 有效

许可证密钥: ENTERPRISE-2024-FULL
类型: 企业版
最大激活数: 100
当前激活数: 0
到期日期: 2027-01-15
状态: 有效

💡 提示：复制上述任意许可证密钥进行测试激活
```

### 在线激活演示

选择"激活许可证"进行在线激活：

```
=== 在线激活许可证 ===

请输入许可证密钥: DEMO-1234-5678-9ABC
请输入邮箱地址: <EMAIL>

🔄 正在激活许可证...
🔄 尝试连接许可证服务器...
✅ 服务器激活成功
✅ 许可证激活成功！
许可证类型: 专业版
到期日期: 2025-01-15
```

### 激活成功后的界面

激活成功后，主界面会显示许可证信息：

```
=== Cursor Reset Tool Pro ===
✅ 已授权 | 用户: <EMAIL> | 类型: 专业版
⏰ 许可证剩余: 365 天

请选择操作：
1. 扫描 Cursor 相关文件和注册表
2. 备份 Cursor 数据
3. 清理 Cursor 数据 (谨慎操作!)
4. 恢复备份
5. 许可证管理
6. 退出
```

## 🔧 核心功能演示

### 1. 扫描功能

```
🔍 扫描 Cursor 相关数据...

📁 扫描文件系统...
✅ 找到: Cursor 应用数据
   路径: C:\Users\<USER>\AppData\Roaming\Cursor
   大小: 15.2 MB

✅ 找到: Cursor 本地数据
   路径: C:\Users\<USER>\AppData\Local\Cursor
   大小: 128.5 MB

🗃️ 扫描注册表...
✅ 找到注册表项: Cursor 主配置
   路径: HKEY_CURRENT_USER\Software\Cursor
   子键: 5, 值: 12

📊 扫描结果汇总:
📁 找到 3 个文件/目录
🗃️ 找到 2 个注册表项

💡 提示：可以使用备份功能保存这些数据，然后使用清理功能删除它们。
```

### 2. 备份功能

```
💾 备份 Cursor 数据...

🔍 扫描需要备份的数据...
💾 创建备份文件: C:\Users\<USER>\Desktop\CursorBackup\CursorBackup_20240115_103000.zip

📁 备份目录: C:\Users\<USER>\AppData\Roaming\Cursor
📄 备份文件: C:\Users\<USER>\Desktop\Cursor.lnk
🗃️ 备份注册表...

✅ 备份完成！
📁 备份文件位置: C:\Users\<USER>\Desktop\CursorBackup\CursorBackup_20240115_103000.zip
```

### 3. 清理功能

```
⚠️  警告：即将清理 Cursor 数据！
这将删除所有 Cursor 相关的配置、缓存和注册表项。
确定要继续吗？(输入 'YES' 确认): YES

🧹 开始清理 Cursor 数据...
⚠️ 请确保已经备份重要数据！

🔄 尝试关闭 Cursor 相关进程...
🔄 关闭进程: Cursor (PID: 1234)
✅ 进程已关闭: Cursor

即将清理 3 个文件/目录和 2 个注册表项
最后确认：输入 'CONFIRM' 继续清理操作：CONFIRM

🗂️ 清理文件和目录...
🗂️ 删除目录: C:\Users\<USER>\AppData\Roaming\Cursor
✅ 目录已删除

🗃️ 清理注册表...
🗃️ 删除注册表项: HKEY_CURRENT_USER\Software\Cursor
✅ 注册表项已删除

🧹 清理其他相关项...
✅ 删除临时文件
✅ 删除最近文件链接
🧹 清理系统缓存...

✅ 清理完成！
💡 建议重启计算机以确保所有更改生效。
```

## 🛡️ 安全特性演示

### 机器绑定

每个许可证都绑定到特定的机器：

```
💻 机器ID: ABC123DEF456GHI789JKL012
```

这个ID基于以下硬件信息生成：
- CPU ID
- 主板序列号
- MAC 地址
- 硬盘序列号

### 加密存储

许可证信息被加密存储在：
- 注册表：`HKEY_CURRENT_USER\SOFTWARE\CursorResetTool`
- 文件系统：`%APPDATA%\CursorResetTool\license.dat`

### 试用期保护

试用信息被加密存储，防止用户篡改：
- 隐藏文件：`%APPDATA%\CursorResetTool\trial.dat`
- 系统属性：隐藏 + 系统文件

## 📊 许可证类型对比

| 功能 | 试用版 | 个人版 | 专业版 | 企业版 |
|------|--------|--------|--------|--------|
| 使用期限 | 7天 | 1年 | 1年 | 3年 |
| 设备数量 | 1台 | 1台 | 3台 | 100台 |
| 技术支持 | 无 | 邮件 | 邮件+电话 | 专属客服 |
| 功能限制 | 无 | 无 | 无 | 无 |

## 🔄 许可证管理

### 重新激活

```
=== 重新激活许可证 ===

🔄 正在重新激活许可证...
✅ 重新激活成功！
```

### 移除许可证

```
=== 移除许可证 ===

确定要移除许可证吗？(输入 'YES' 确认): YES
✅ 许可证已移除
```

### 在线验证

```
=== 检查在线状态 ===

🔄 正在验证许可证...
✅ 许可证在线验证成功
```

## 💡 使用技巧

1. **首次使用**：建议先使用试用版熟悉功能
2. **备份重要**：清理前务必创建备份
3. **管理员权限**：程序需要管理员权限才能正常工作
4. **网络连接**：激活时需要网络连接（或使用离线激活）
5. **许可证保存**：激活后许可证会自动保存，无需重复激活

## 🚨 注意事项

- 此工具仅用于学习和演示目的
- 请遵守相关软件的使用条款
- 清理操作不可逆，请谨慎使用
- 建议在虚拟机中测试

---

**这个项目展示了一个完整的软件授权系统的实现，包括网络验证、加密存储、硬件绑定等企业级功能。**
