@echo off
title 尚书CAR - WPF启动器

echo ========================================
echo    尚书_CAR_cursor重置系统 - 启动器
echo ========================================
echo.

REM Get current directory
set CURRENT_DIR=%~dp0

REM Define program paths
set PROGRAM_PATH=%CURRENT_DIR%CursorResetTool-Demo\bin\Debug\net8.0-windows\CursorResetTool-Demo.exe

echo 正在检查程序文件...

if exist "%PROGRAM_PATH%" (
    echo [OK] 找到程序文件
    echo.
    echo 正在启动 WPF 图形界面程序...
    echo.
    
    REM Start the WPF application
    start "" "%PROGRAM_PATH%"
    
    echo ========================================
    echo    程序启动成功！
    echo ========================================
    echo.
    echo 如果程序界面没有显示，可能需要：
    echo.
    echo 1. 安装 .NET 8.0 Desktop Runtime
    echo    下载地址：https://dotnet.microsoft.com/download/dotnet/8.0
    echo    选择：Desktop Runtime x64
    echo.
    echo 2. 检查任务管理器中是否有程序进程
    echo.
    echo 3. 确保有足够的系统权限
    echo.
    echo ========================================
    echo.
    echo 程序特性：
    echo • 现代化 Material Design 界面
    echo • 完整的 Cursor 重置功能
    echo • 智能扫描和安全备份
    echo • 企业级用户体验
    echo.
    
    timeout /t 5 /nobreak >nul
    
) else (
    echo [错误] 未找到程序文件！
    echo.
    echo 程序路径：%PROGRAM_PATH%
    echo.
    echo 请检查：
    echo 1. CursorResetTool-Demo 文件夹是否存在
    echo 2. 程序是否已编译
    echo.
    echo 如需重新编译，请运行：
    echo   cd CursorResetTool-Demo
    echo   dotnet build
    echo.
    pause
)

exit
