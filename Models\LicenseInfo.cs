using System;

namespace CursorReset.Models
{
    public class LicenseInfo
    {
        public string LicenseKey { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public DateTime IssueDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public LicenseType Type { get; set; }
        public string MachineId { get; set; } = string.Empty;
        public bool IsActivated { get; set; }
        public int MaxActivations { get; set; } = 1;
        public int CurrentActivations { get; set; } = 0;
        public string ProductVersion { get; set; } = "1.0.0";
        public string Signature { get; set; } = string.Empty;
    }

    public enum LicenseType
    {
        Trial = 0,
        Personal = 1,
        Professional = 2,
        Enterprise = 3
    }

    public class ActivationRequest
    {
        public string LicenseKey { get; set; } = string.Empty;
        public string MachineId { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public string ProductVersion { get; set; } = string.Empty;
        public string OSInfo { get; set; } = string.Empty;
    }

    public class ActivationResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public LicenseInfo? License { get; set; }
        public string ErrorCode { get; set; } = string.Empty;
    }

    public class TrialInfo
    {
        public DateTime FirstRun { get; set; }
        public DateTime LastRun { get; set; }
        public int RunCount { get; set; }
        public int TrialDays { get; set; } = 7;
        public bool IsExpired => DateTime.Now > FirstRun.AddDays(TrialDays);
        public int RemainingDays => Math.Max(0, TrialDays - (DateTime.Now - FirstRun).Days);
    }
}
