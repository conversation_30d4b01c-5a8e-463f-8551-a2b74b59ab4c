using System;

namespace CursorReset.Test
{
    class TestProgram
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Cursor Reset Tool Pro - 编译测试 ===");
            Console.WriteLine();
            
            try
            {
                Console.WriteLine("✅ 程序启动成功");
                Console.WriteLine("✅ 基本功能正常");
                
                // 测试日志服务
                Console.WriteLine("🧪 测试日志服务...");
                Services.LoggingService.LogInfo("Test log message");
                Console.WriteLine("✅ 日志服务正常");
                
                // 测试加密服务
                Console.WriteLine("🧪 测试加密服务...");
                var machineId = Services.CryptographyService.GenerateMachineId();
                Console.WriteLine($"✅ 机器ID生成成功: {machineId.Substring(0, 8)}...");
                
                // 测试许可证服务
                Console.WriteLine("🧪 测试许可证服务...");
                var isValid = Services.LicenseService.IsLicenseValid();
                Console.WriteLine($"✅ 许可证检查完成: {(isValid ? "有效" : "无效")}");
                
                Console.WriteLine();
                Console.WriteLine("🎉 所有测试通过！");
                Console.WriteLine("程序编译和基本功能正常。");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
