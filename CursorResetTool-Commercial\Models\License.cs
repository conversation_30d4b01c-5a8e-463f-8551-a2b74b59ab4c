using System.ComponentModel.DataAnnotations;

namespace CursorResetTool.Commercial.Models
{
    /// <summary>
    /// 许可证模型
    /// </summary>
    public class License
    {
        /// <summary>
        /// 许可证ID
        /// </summary>
        public string LicenseId { get; set; } = string.Empty;

        /// <summary>
        /// 许可证密钥
        /// </summary>
        [Required(ErrorMessage = "许可证密钥不能为空")]
        public string LicenseKey { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 用户邮箱
        /// </summary>
        public string UserEmail { get; set; } = string.Empty;

        /// <summary>
        /// 许可证类型
        /// </summary>
        public LicenseType LicenseType { get; set; } = LicenseType.Trial;

        /// <summary>
        /// 发行日期
        /// </summary>
        public DateTime IssueDate { get; set; } = DateTime.Now;

        /// <summary>
        /// 到期日期
        /// </summary>
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// 最大激活设备数
        /// </summary>
        public int MaxDevices { get; set; } = 1;

        /// <summary>
        /// 已激活设备数
        /// </summary>
        public int ActivatedDevices { get; set; } = 0;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 激活时间
        /// </summary>
        public DateTime? ActivationTime { get; set; }

        /// <summary>
        /// 机器码
        /// </summary>
        public string MachineCode { get; set; } = string.Empty;

        /// <summary>
        /// 硬件指纹
        /// </summary>
        public string HardwareFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// 数字签名
        /// </summary>
        public string DigitalSignature { get; set; } = string.Empty;

        /// <summary>
        /// 许可证状态
        /// </summary>
        public LicenseStatus Status { get; set; } = LicenseStatus.Pending;

        /// <summary>
        /// 最后验证时间
        /// </summary>
        public DateTime? LastValidationTime { get; set; }

        /// <summary>
        /// 验证次数
        /// </summary>
        public int ValidationCount { get; set; } = 0;

        /// <summary>
        /// 备注信息
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// 检查许可证是否有效
        /// </summary>
        public bool IsValid => IsActive && Status == LicenseStatus.Active && DateTime.Now <= ExpiryDate;

        /// <summary>
        /// 获取剩余天数
        /// </summary>
        public int RemainingDays => IsValid ? (ExpiryDate - DateTime.Now).Days : 0;
    }

    /// <summary>
    /// 许可证类型枚举
    /// </summary>
    public enum LicenseType
    {
        /// <summary>
        /// 试用版
        /// </summary>
        Trial = 0,

        /// <summary>
        /// 个人版
        /// </summary>
        Personal = 1,

        /// <summary>
        /// 专业版
        /// </summary>
        Professional = 2,

        /// <summary>
        /// 企业版
        /// </summary>
        Enterprise = 3
    }

    /// <summary>
    /// 许可证状态枚举
    /// </summary>
    public enum LicenseStatus
    {
        /// <summary>
        /// 待激活
        /// </summary>
        Pending = 0,

        /// <summary>
        /// 已激活
        /// </summary>
        Active = 1,

        /// <summary>
        /// 已过期
        /// </summary>
        Expired = 2,

        /// <summary>
        /// 已撤销
        /// </summary>
        Revoked = 3,

        /// <summary>
        /// 已暂停
        /// </summary>
        Suspended = 4
    }

    /// <summary>
    /// 设备信息模型
    /// </summary>
    public class DeviceInfo
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string DeviceId { get; set; } = string.Empty;

        /// <summary>
        /// 许可证ID
        /// </summary>
        public string LicenseId { get; set; } = string.Empty;

        /// <summary>
        /// 机器码
        /// </summary>
        public string MachineCode { get; set; } = string.Empty;

        /// <summary>
        /// 硬件指纹
        /// </summary>
        public string HardwareFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; } = string.Empty;

        /// <summary>
        /// 操作系统
        /// </summary>
        public string OperatingSystem { get; set; } = string.Empty;

        /// <summary>
        /// 首次激活时间
        /// </summary>
        public DateTime FirstActivationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivityTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否活跃
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
}
