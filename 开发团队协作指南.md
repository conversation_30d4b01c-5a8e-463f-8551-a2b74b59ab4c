# Cursor Reset Tool Pro - 开发团队协作指南

## 👥 团队角色分工

### 项目经理 (Project Manager)
- 项目进度管理和协调
- 需求分析和优先级排序
- 风险评估和问题解决
- 与客户沟通和反馈收集

### 架构师 (Software Architect)
- 系统架构设计和技术选型
- 代码审查和技术指导
- 性能优化和安全设计
- 技术文档编写

### 前端开发 (Frontend Developer)
- 用户界面设计和实现
- 用户体验优化
- 界面交互逻辑
- 客户端验证

### 后端开发 (Backend Developer)
- 服务器端逻辑实现
- 数据库设计和优化
- API 接口开发
- 安全机制实现

### 测试工程师 (QA Engineer)
- 测试用例设计和执行
- 自动化测试脚本
- 性能测试和压力测试
- 缺陷跟踪和验证

### 运维工程师 (DevOps Engineer)
- 部署流程设计
- 监控和日志系统
- 自动化构建和发布
- 服务器维护

## 🔄 开发流程

### 1. 需求分析阶段
```
需求收集 → 需求分析 → 技术评估 → 工作量估算 → 计划制定
```

**输出文档:**
- 需求规格说明书
- 技术方案设计
- 项目计划和里程碑

### 2. 设计阶段
```
系统设计 → 数据库设计 → 接口设计 → UI设计 → 安全设计
```

**输出文档:**
- 系统架构图
- 数据库设计文档
- API 接口文档
- UI 设计稿

### 3. 开发阶段
```
环境搭建 → 编码实现 → 单元测试 → 代码审查 → 集成测试
```

**开发规范:**
- 遵循编码规范
- 编写单元测试
- 提交代码审查
- 更新文档

### 4. 测试阶段
```
功能测试 → 集成测试 → 性能测试 → 安全测试 → 用户验收测试
```

**测试类型:**
- 单元测试 (Unit Testing)
- 集成测试 (Integration Testing)
- 系统测试 (System Testing)
- 验收测试 (Acceptance Testing)

### 5. 部署阶段
```
预发布环境 → 生产环境部署 → 监控配置 → 文档更新 → 培训支持
```

## 📋 Git 工作流程

### 分支策略
```
main (生产环境)
├── develop (开发环境)
│   ├── feature/license-system (功能分支)
│   ├── feature/ui-improvement (功能分支)
│   └── feature/security-enhancement (功能分支)
├── release/v1.0.0 (发布分支)
└── hotfix/critical-bug (热修复分支)
```

### 提交规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

**类型 (type):**
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例:**
```
feat(license): add network activation feature

- Implement online license activation
- Add offline activation fallback
- Integrate with mock license server

Closes #123
```

### 代码审查流程
1. **创建 Pull Request**
   - 描述清楚变更内容
   - 关联相关 Issue
   - 添加测试用例

2. **审查检查项**
   - 代码质量和规范
   - 功能实现正确性
   - 测试覆盖率
   - 文档更新

3. **审查反馈**
   - 及时响应审查意见
   - 修改后重新提交
   - 获得批准后合并

## 🛠️ 开发环境配置

### 必需软件
```bash
# .NET 8.0 SDK
winget install Microsoft.DotNet.SDK.8

# Visual Studio 2022 或 VS Code
winget install Microsoft.VisualStudio.2022.Community
# 或
winget install Microsoft.VisualStudioCode

# Git
winget install Git.Git

# SQL Server (可选，用于真实数据库)
winget install Microsoft.SQLServer.2022.Developer
```

### 开发工具配置

#### Visual Studio 扩展
- **SonarLint** - 代码质量检查
- **CodeMaid** - 代码清理和格式化
- **GitLens** - Git 增强功能
- **Resharper** - 代码分析和重构

#### VS Code 扩展
- **C# Dev Kit** - C# 开发支持
- **GitLens** - Git 增强功能
- **SonarLint** - 代码质量检查
- **Thunder Client** - API 测试

### 项目配置文件

#### .editorconfig
```ini
root = true

[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
indent_style = space
indent_size = 4

[*.{cs,csx}]
indent_size = 4

[*.{json,yml,yaml}]
indent_size = 2
```

#### .gitignore
```
# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/

# Visual Studio
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# Logs
logs/
*.log

# License files (exclude real keys)
license_real.dat
production_keys.json
```

## 📊 质量保证

### 代码质量指标
- **代码覆盖率**: ≥ 80%
- **圈复杂度**: ≤ 10
- **代码重复率**: ≤ 5%
- **技术债务**: 控制在合理范围

### 代码审查清单
```markdown
## 功能性
- [ ] 功能实现符合需求
- [ ] 边界条件处理正确
- [ ] 错误处理完善
- [ ] 性能满足要求

## 代码质量
- [ ] 代码结构清晰
- [ ] 命名规范一致
- [ ] 注释充分准确
- [ ] 无重复代码

## 安全性
- [ ] 输入验证完整
- [ ] 敏感信息保护
- [ ] 权限控制正确
- [ ] 加密实现安全

## 测试
- [ ] 单元测试充分
- [ ] 集成测试通过
- [ ] 测试用例覆盖边界
- [ ] 性能测试满足要求
```

### 自动化检查
```yaml
# GitHub Actions 示例
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x
        
    - name: Restore dependencies
      run: dotnet restore
      
    - name: Build
      run: dotnet build --no-restore
      
    - name: Test
      run: dotnet test --no-build --verbosity normal --collect:"XPlat Code Coverage"
      
    - name: Code Coverage
      uses: codecov/codecov-action@v3
```

## 📝 文档管理

### 文档类型
1. **技术文档**
   - 系统架构文档
   - API 接口文档
   - 数据库设计文档
   - 部署运维文档

2. **用户文档**
   - 用户使用手册
   - 安装配置指南
   - 常见问题解答
   - 视频教程

3. **开发文档**
   - 开发环境搭建
   - 编码规范
   - 测试指南
   - 发布流程

### 文档维护
- **版本控制**: 文档与代码同步更新
- **审查机制**: 重要文档需要审查
- **定期更新**: 定期检查文档准确性
- **格式统一**: 使用统一的文档模板

## 🚀 发布管理

### 版本号规范
采用语义化版本控制 (Semantic Versioning):
```
主版本号.次版本号.修订号 (MAJOR.MINOR.PATCH)

例如: 1.2.3
- 1: 主版本号 (不兼容的API修改)
- 2: 次版本号 (向下兼容的功能性新增)
- 3: 修订号 (向下兼容的问题修正)
```

### 发布流程
1. **代码冻结** - 停止新功能开发
2. **测试验证** - 完整的测试验证
3. **文档更新** - 更新相关文档
4. **版本标记** - 创建版本标签
5. **构建发布** - 生成发布包
6. **部署验证** - 部署到生产环境
7. **发布公告** - 发布版本说明

### 发布检查清单
```markdown
## 发布前检查
- [ ] 所有测试通过
- [ ] 代码审查完成
- [ ] 文档更新完整
- [ ] 版本号正确
- [ ] 发布说明准备

## 发布过程
- [ ] 创建发布分支
- [ ] 构建发布包
- [ ] 部署到预发布环境
- [ ] 验证功能正常
- [ ] 部署到生产环境

## 发布后
- [ ] 监控系统状态
- [ ] 收集用户反馈
- [ ] 准备热修复计划
- [ ] 更新项目文档
```

## 📞 沟通协作

### 会议安排
- **每日站会** (Daily Standup): 15分钟，同步进度和问题
- **迭代计划** (Sprint Planning): 2小时，制定迭代计划
- **迭代回顾** (Sprint Retrospective): 1小时，总结改进
- **技术分享** (Tech Talk): 每周1次，技术交流

### 沟通工具
- **即时通讯**: Microsoft Teams / Slack
- **项目管理**: Azure DevOps / Jira
- **文档协作**: Confluence / SharePoint
- **代码托管**: GitHub / Azure Repos

### 问题升级机制
```
Level 1: 开发团队内部解决 (2小时内)
    ↓
Level 2: 技术负责人介入 (4小时内)
    ↓
Level 3: 项目经理协调 (8小时内)
    ↓
Level 4: 管理层决策 (24小时内)
```

---

**这个协作指南为开发团队提供了完整的工作流程和规范，确保项目的高质量交付和团队的高效协作。**
