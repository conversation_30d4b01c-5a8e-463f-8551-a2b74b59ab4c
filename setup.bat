@echo off
chcp 65001 >nul
echo ========================================
echo    Cursor Reset Tool 安装脚本
echo ========================================
echo.

echo 检查 .NET 8.0 SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到 .NET 8.0 SDK
    echo.
    echo 请按照以下步骤安装 .NET 8.0 SDK:
    echo 1. 访问: https://dotnet.microsoft.com/download/dotnet/8.0
    echo 2. 下载并安装 .NET 8.0 SDK
    echo 3. 重启命令提示符
    echo 4. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo ✅ 检测到 .NET SDK
dotnet --version

echo.
echo 编译项目...
dotnet build
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo ✅ 编译成功
echo.
echo ⚠️ 重要提醒:
echo - 此工具需要管理员权限运行
echo - 仅用于学习目的
echo - 使用前请备份重要数据
echo.
echo 按任意键以管理员身份运行程序...
pause >nul

echo 启动程序...
powershell -Command "Start-Process 'dotnet' -ArgumentList 'run' -Verb RunAs"
