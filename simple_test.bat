@echo off
echo ========================================
echo    Cursor Reset Tool Pro - Test Script
echo ========================================
echo.

echo Step 1: Check .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not found
    pause
    exit /b 1
)
echo SUCCESS: .NET SDK available

echo.
echo Step 2: Clean and restore...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Package restore failed
    pause
    exit /b 1
)
echo SUCCESS: Package restore completed

echo.
echo Step 3: Build project...
dotnet build -c Debug
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)
echo SUCCESS: Build completed

echo.
echo Step 4: Check executable...
if not exist "bin\Debug\net8.0\CursorReset.exe" (
    echo ERROR: Executable not found
    pause
    exit /b 1
)
echo SUCCESS: Executable generated

echo.
echo Step 5: Check admin privileges...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: No administrator privileges
    echo The program requires admin rights to run properly
) else (
    echo SUCCESS: Administrator privileges confirmed
)

echo.
echo Step 6: Test compilation completed successfully!
echo.
echo Would you like to run the program now? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo.
    echo Starting program...
    echo Suggested test items:
    echo 1. License activation (use test keys)
    echo 2. Scan functionality  
    echo 3. Log viewing
    echo 4. Error handling
    echo.
    pause
    dotnet run --no-build
)

echo.
echo Test script completed.
pause
