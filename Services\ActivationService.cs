using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using CursorReset.Models;
using CursorReset.Server;

namespace CursorReset.Services
{
    public class ActivationService
    {
        private static readonly string ActivationServerUrl = "https://api.cursorreset.com/activate"; // 示例URL
        private static readonly string ValidationServerUrl = "https://api.cursorreset.com/validate"; // 示例URL
        private static readonly HttpClient httpClient = new();

        static ActivationService()
        {
            httpClient.Timeout = TimeSpan.FromSeconds(30);
            httpClient.DefaultRequestHeaders.Add("User-Agent", "CursorResetTool/1.0");
        }

        public static async Task<ActivationResponse> ActivateLicenseAsync(string licenseKey, string userEmail)
        {
            try
            {
                var request = new ActivationRequest
                {
                    LicenseKey = licenseKey,
                    MachineId = CryptographyService.GenerateMachineId(),
                    UserEmail = userEmail,
                    ProductVersion = "1.0.0",
                    OSInfo = GetOSInfo()
                };

                // 首先尝试使用模拟服务器（用于演示）
                Console.WriteLine("🔄 尝试连接许可证服务器...");
                await Task.Delay(1000); // 模拟网络延迟

                var mockResponse = MockLicenseServer.ProcessActivation(request);

                if (mockResponse.Success && mockResponse.License != null)
                {
                    // 保存许可证到本地
                    LicenseService.SaveLicense(mockResponse.License);
                    Console.WriteLine("✅ 服务器激活成功");
                }
                else
                {
                    Console.WriteLine($"❌ 服务器激活失败: {mockResponse.Message}");
                }

                return mockResponse;

                // 以下是真实网络请求的代码（在实际部署时启用）
                /*
                var jsonRequest = JsonSerializer.Serialize(request);
                var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

                var response = await httpClient.PostAsync(ActivationServerUrl, content);
                var jsonResponse = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var activationResponse = JsonSerializer.Deserialize<ActivationResponse>(jsonResponse);

                    if (activationResponse?.Success == true && activationResponse.License != null)
                    {
                        // 保存许可证到本地
                        LicenseService.SaveLicense(activationResponse.License);
                    }

                    return activationResponse ?? new ActivationResponse { Success = false, Message = "响应解析失败" };
                }
                else
                {
                    return new ActivationResponse
                    {
                        Success = false,
                        Message = $"服务器错误: {response.StatusCode}",
                        ErrorCode = response.StatusCode.ToString()
                    };
                }
                */
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 网络激活失败: {ex.Message}");

                // 网络失败时尝试离线激活
                return ActivateLicenseOffline(licenseKey, userEmail);
            }
        }

        public static async Task<bool> ValidateLicenseOnlineAsync()
        {
            try
            {
                var license = LicenseService.LoadLicense();
                if (license == null) return false;

                var request = new
                {
                    LicenseKey = license.LicenseKey,
                    MachineId = license.MachineId,
                    ProductVersion = license.ProductVersion
                };

                var jsonRequest = JsonSerializer.Serialize(request);
                var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

                var response = await httpClient.PostAsync(ValidationServerUrl, content);
                
                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var validationResponse = JsonSerializer.Deserialize<ActivationResponse>(jsonResponse);
                    return validationResponse?.Success == true;
                }

                return false;
            }
            catch
            {
                // 网络验证失败时，依赖本地验证
                return LicenseService.IsLicenseValid();
            }
        }

        public static ActivationResponse ActivateLicenseOffline(string licenseKey, string userEmail)
        {
            try
            {
                // 离线激活逻辑（用于演示）
                if (IsValidLicenseKeyFormat(licenseKey))
                {
                    var license = new LicenseInfo
                    {
                        LicenseKey = licenseKey,
                        UserEmail = userEmail,
                        UserName = userEmail.Split('@')[0],
                        IssueDate = DateTime.Now,
                        ExpiryDate = DateTime.Now.AddYears(1),
                        Type = LicenseType.Professional,
                        MachineId = CryptographyService.GenerateMachineId(),
                        IsActivated = true,
                        MaxActivations = 1,
                        CurrentActivations = 1,
                        ProductVersion = "1.0.0"
                    };

                    // 生成签名
                    var dataToSign = $"{license.LicenseKey}{license.UserEmail}{license.ExpiryDate:yyyy-MM-dd}{license.MachineId}";
                    license.Signature = CryptographyService.GenerateSignature(dataToSign, "PrivateKey");

                    // 保存许可证
                    if (LicenseService.SaveLicense(license))
                    {
                        return new ActivationResponse
                        {
                            Success = true,
                            Message = "离线激活成功",
                            License = license
                        };
                    }
                }

                return new ActivationResponse
                {
                    Success = false,
                    Message = "无效的许可证密钥",
                    ErrorCode = "INVALID_KEY"
                };
            }
            catch (Exception ex)
            {
                return new ActivationResponse
                {
                    Success = false,
                    Message = $"离线激活失败: {ex.Message}",
                    ErrorCode = "OFFLINE_ERROR"
                };
            }
        }

        private static bool IsValidLicenseKeyFormat(string licenseKey)
        {
            if (string.IsNullOrWhiteSpace(licenseKey)) return false;
            
            // 检查格式: XXXX-XXXX-XXXX-XXXX
            var parts = licenseKey.Split('-');
            if (parts.Length != 4) return false;
            
            foreach (var part in parts)
            {
                if (part.Length != 4) return false;
                foreach (var c in part)
                {
                    if (!char.IsLetterOrDigit(c)) return false;
                }
            }
            
            return true;
        }

        private static string GetOSInfo()
        {
            try
            {
                return $"{Environment.OSVersion} ({Environment.Is64BitOperatingSystem})";
            }
            catch
            {
                return "Unknown OS";
            }
        }

        public static string GenerateTrialLicense()
        {
            var trialLicense = new LicenseInfo
            {
                LicenseKey = "TRIAL-" + CryptographyService.GenerateLicenseKey(),
                UserEmail = "<EMAIL>",
                UserName = "Trial User",
                IssueDate = DateTime.Now,
                ExpiryDate = DateTime.Now.AddDays(7),
                Type = LicenseType.Trial,
                MachineId = CryptographyService.GenerateMachineId(),
                IsActivated = true,
                MaxActivations = 1,
                CurrentActivations = 1,
                ProductVersion = "1.0.0"
            };

            var dataToSign = $"{trialLicense.LicenseKey}{trialLicense.UserEmail}{trialLicense.ExpiryDate:yyyy-MM-dd}{trialLicense.MachineId}";
            trialLicense.Signature = CryptographyService.GenerateSignature(dataToSign, "PrivateKey");

            LicenseService.SaveLicense(trialLicense);
            return trialLicense.LicenseKey;
        }
    }
}
