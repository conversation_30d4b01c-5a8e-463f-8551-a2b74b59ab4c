@echo off
chcp 65001 >nul
echo ========================================
echo    Cursor Reset Tool Pro - 构建脚本
echo ========================================
echo.

echo 检查 .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到 .NET SDK
    echo 请安装 .NET 8.0 SDK
    pause
    exit /b 1
)

echo ✅ .NET SDK 可用
echo.

echo 清理旧的构建文件...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "publish" rmdir /s /q "publish"

echo.
echo 恢复 NuGet 包...
dotnet restore
if %errorlevel% neq 0 (
    echo ❌ 包恢复失败
    pause
    exit /b 1
)

echo.
echo 编译项目...
dotnet build -c Release
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo.
echo 发布单文件版本...

echo 发布 Windows x64 版本...
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o "publish\win-x64"
if %errorlevel% neq 0 (
    echo ❌ x64 发布失败
    pause
    exit /b 1
)

echo 发布 Windows x86 版本...
dotnet publish -c Release -r win-x86 --self-contained true -p:PublishSingleFile=true -o "publish\win-x86"
if %errorlevel% neq 0 (
    echo ❌ x86 发布失败
    pause
    exit /b 1
)

echo.
echo 创建发布包...
mkdir "publish\release" 2>nul

echo 复制文件到发布目录...
copy "publish\win-x64\CursorReset.exe" "publish\release\CursorResetTool-x64.exe"
copy "publish\win-x86\CursorReset.exe" "publish\release\CursorResetTool-x86.exe"
copy "README.md" "publish\release\"
copy "部署指南.md" "publish\release\"
copy "使用说明.txt" "publish\release\"

echo.
echo 创建启动脚本...
echo @echo off > "publish\release\运行工具-x64.bat"
echo chcp 65001 ^>nul >> "publish\release\运行工具-x64.bat"
echo echo 启动 Cursor Reset Tool Pro... >> "publish\release\运行工具-x64.bat"
echo echo. >> "publish\release\运行工具-x64.bat"
echo powershell -Command "Start-Process 'CursorResetTool-x64.exe' -Verb RunAs" >> "publish\release\运行工具-x64.bat"

echo @echo off > "publish\release\运行工具-x86.bat"
echo chcp 65001 ^>nul >> "publish\release\运行工具-x86.bat"
echo echo 启动 Cursor Reset Tool Pro... >> "publish\release\运行工具-x86.bat"
echo echo. >> "publish\release\运行工具-x86.bat"
echo powershell -Command "Start-Process 'CursorResetTool-x86.exe' -Verb RunAs" >> "publish\release\运行工具-x86.bat"

echo.
echo 创建许可证说明...
echo Cursor Reset Tool Pro - 许可证说明 > "publish\release\许可证说明.txt"
echo ================================== >> "publish\release\许可证说明.txt"
echo. >> "publish\release\许可证说明.txt"
echo 测试许可证密钥： >> "publish\release\许可证说明.txt"
echo. >> "publish\release\许可证说明.txt"
echo 1. DEMO-1234-5678-9ABC >> "publish\release\许可证说明.txt"
echo    类型：专业版 >> "publish\release\许可证说明.txt"
echo    激活数：3个设备 >> "publish\release\许可证说明.txt"
echo. >> "publish\release\许可证说明.txt"
echo 2. TEST-ABCD-EFGH-IJKL >> "publish\release\许可证说明.txt"
echo    类型：个人版 >> "publish\release\许可证说明.txt"
echo    激活数：1个设备 >> "publish\release\许可证说明.txt"
echo. >> "publish\release\许可证说明.txt"
echo 3. ENTERPRISE-2024-FULL >> "publish\release\许可证说明.txt"
echo    类型：企业版 >> "publish\release\许可证说明.txt"
echo    激活数：100个设备 >> "publish\release\许可证说明.txt"
echo. >> "publish\release\许可证说明.txt"
echo 使用方法： >> "publish\release\许可证说明.txt"
echo 1. 以管理员身份运行程序 >> "publish\release\许可证说明.txt"
echo 2. 选择"许可证管理" >> "publish\release\许可证说明.txt"
echo 3. 选择"激活许可证" >> "publish\release\许可证说明.txt"
echo 4. 输入上述任意许可证密钥 >> "publish\release\许可证说明.txt"
echo 5. 输入任意邮箱地址 >> "publish\release\许可证说明.txt"
echo 6. 等待激活完成 >> "publish\release\许可证说明.txt"

echo.
echo ✅ 构建完成！
echo.
echo 📁 发布文件位置：
echo    publish\release\
echo.
echo 📦 包含文件：
echo    - CursorResetTool-x64.exe (64位版本)
echo    - CursorResetTool-x86.exe (32位版本)
echo    - 运行工具-x64.bat (64位启动脚本)
echo    - 运行工具-x86.bat (32位启动脚本)
echo    - README.md (项目说明)
echo    - 部署指南.md (部署文档)
echo    - 使用说明.txt (使用指南)
echo    - 许可证说明.txt (测试许可证)
echo.
echo 💡 提示：
echo    - 程序需要管理员权限运行
echo    - 首次运行会有7天试用期
echo    - 可以使用测试许可证进行激活
echo.
pause
