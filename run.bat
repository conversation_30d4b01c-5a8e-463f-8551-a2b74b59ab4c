@echo off
chcp 65001 >nul
title Cursor Reset Tool Pro - 开发环境启动器

echo ========================================
echo   Cursor Reset Tool Pro - 开发环境
echo ========================================
echo.

echo 🔍 检查开发环境...

echo 检查 .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到 .NET SDK
    echo.
    echo 💡 解决方案：
    echo   1. 安装 .NET 8.0 SDK
    echo   2. 运行 setup.bat 安装依赖
    echo   3. 重新启动此脚本
    pause
    exit /b 1
)

echo ✅ .NET SDK 可用
dotnet --version

echo.
echo 检查项目文件...
if not exist "CursorReset.csproj" (
    echo ❌ 找不到项目文件 CursorReset.csproj
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo ✅ 项目文件确认

echo.
echo 🔧 编译项目...
dotnet build
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    echo 请检查代码错误并修复后重试
    pause
    exit /b 1
)

echo ✅ 编译成功

echo.
echo 🔐 检查管理员权限...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ 需要管理员权限运行
    echo 正在以管理员身份重新启动...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo ✅ 管理员权限确认

echo.
echo 🚀 启动 Cursor Reset Tool Pro (开发版)...
echo.
echo 💡 开发提示：
echo   - 这是开发环境版本
echo   - 所有日志和错误信息会详细显示
echo   - 修改代码后需要重新运行此脚本
echo.

dotnet run

echo.
echo 📋 程序已退出
echo.
echo 💡 开发提示：
echo   - 查看日志：%%APPDATA%%\CursorResetTool\logs\
echo   - 如需调试，可以使用 Visual Studio 或 VS Code
echo   - 发布版本请运行 build.bat
echo.
pause
