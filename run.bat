@echo off
chcp 65001 >nul
echo ========================================
echo    Cursor Reset Tool
echo ========================================
echo.

echo 检查管理员权限...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 需要管理员权限
    echo 正在以管理员身份重新启动...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo ✅ 管理员权限确认
echo.

echo 检查 .NET SDK...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到 .NET SDK
    echo 请先运行 setup.bat 安装依赖
    pause
    exit /b 1
)

echo ✅ .NET SDK 可用
echo.

echo 启动 Cursor Reset Tool...
echo.
dotnet run

echo.
echo 程序已退出
pause
