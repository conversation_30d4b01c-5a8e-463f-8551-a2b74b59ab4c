<Window x:Class="CursorResetTool.Commercial.Views.SplashWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="尚书_CAR_cursor重置系统"
        Width="600" Height="400"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Topmost="True">

    <Window.Resources>
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:1"/>
        </Storyboard>
        
        <Storyboard x:Key="ProgressAnimation" RepeatBehavior="Forever">
            <DoubleAnimation Storyboard.TargetName="ProgressRotate"
                           Storyboard.TargetProperty="Angle"
                           From="0" To="360" Duration="0:0:2"/>
        </Storyboard>
    </Window.Resources>

    <Window.Triggers>
        <EventTrigger RoutedEvent="Loaded">
            <BeginStoryboard Storyboard="{StaticResource FadeInAnimation}"/>
            <BeginStoryboard Storyboard="{StaticResource ProgressAnimation}"/>
        </EventTrigger>
    </Window.Triggers>

    <Border Background="{StaticResource SurfaceBrush}"
            CornerRadius="16"
            BorderThickness="1"
            BorderBrush="{StaticResource PrimaryBrush}">
        <Border.Effect>
            <DropShadowEffect Color="Black" Opacity="0.3" ShadowDepth="8" BlurRadius="20"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>

            <!-- 背景装饰 -->
            <Canvas Grid.RowSpan="5" ClipToBounds="True">
                <Ellipse Width="200" Height="200" 
                        Canvas.Right="-100" Canvas.Top="-100"
                        Fill="{StaticResource PrimaryBrush}" Opacity="0.1"/>
                <Ellipse Width="150" Height="150" 
                        Canvas.Left="-75" Canvas.Bottom="-75"
                        Fill="{StaticResource SecondaryBrush}" Opacity="0.1"/>
            </Canvas>

            <!-- Logo和标题区域 -->
            <StackPanel Grid.Row="0" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center"
                       Margin="40">
                
                <!-- Logo -->
                <Border Width="120" Height="120"
                       Background="{StaticResource PrimaryBrush}"
                       CornerRadius="60"
                       Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="{Binding Source={StaticResource PrimaryBrush}, Path=Color}" 
                                        Opacity="0.4" ShadowDepth="4" BlurRadius="12"/>
                    </Border.Effect>
                    
                    <materialDesign:PackIcon Kind="CursorDefault"
                                           Width="60" Height="60"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                </Border>

                <!-- 应用程序名称 -->
                <TextBlock Text="尚书_CAR"
                          Style="{StaticResource TitleTextStyle}"
                          FontSize="32"
                          FontWeight="Bold"
                          HorizontalAlignment="Center"
                          Foreground="{StaticResource PrimaryBrush}"
                          Margin="0,0,0,8"/>

                <TextBlock Text="Cursor重置系统"
                          Style="{StaticResource SubtitleTextStyle}"
                          FontSize="18"
                          HorizontalAlignment="Center"
                          Foreground="{StaticResource TextSecondaryBrush}"
                          Margin="0,0,0,8"/>

                <!-- 版本信息 -->
                <TextBlock x:Name="VersionText"
                          Text="版本 2.0.0"
                          Style="{StaticResource BodyTextStyle}"
                          FontSize="12"
                          HorizontalAlignment="Center"
                          Foreground="{StaticResource TextSecondaryBrush}"/>
            </StackPanel>

            <!-- 进度指示器 -->
            <StackPanel Grid.Row="1" 
                       HorizontalAlignment="Center"
                       Margin="40,0">
                
                <Canvas Width="40" Height="40" Margin="0,0,0,16">
                    <Ellipse Width="40" Height="40"
                            Stroke="{StaticResource PrimaryBrush}"
                            StrokeThickness="3"
                            Opacity="0.3"/>
                    
                    <Path Stroke="{StaticResource PrimaryBrush}"
                          StrokeThickness="3"
                          Data="M 20,2 A 18,18 0 0,1 38,20">
                        <Path.RenderTransform>
                            <RotateTransform x:Name="ProgressRotate" CenterX="20" CenterY="20"/>
                        </Path.RenderTransform>
                    </Path>
                </Canvas>

                <!-- 状态文本 -->
                <TextBlock x:Name="StatusText"
                          Text="正在初始化..."
                          Style="{StaticResource BodyTextStyle}"
                          FontSize="14"
                          HorizontalAlignment="Center"
                          Foreground="{StaticResource TextSecondaryBrush}"/>
            </StackPanel>

            <!-- 进度条 -->
            <ProgressBar x:Name="ProgressBar"
                        Grid.Row="2"
                        Style="{StaticResource ModernProgressBarStyle}"
                        Height="4"
                        Margin="60,16,60,0"
                        Value="0"
                        Maximum="100"/>

            <!-- 底部信息 -->
            <StackPanel Grid.Row="3" 
                       HorizontalAlignment="Center"
                       Margin="40,16,40,0">
                
                <TextBlock Text="专业的Cursor编辑器重置工具"
                          Style="{StaticResource BodyTextStyle}"
                          FontSize="12"
                          HorizontalAlignment="Center"
                          Foreground="{StaticResource TextSecondaryBrush}"
                          Margin="0,0,0,4"/>

                <TextBlock Text="Copyright © 2024 尚书科技. All rights reserved."
                          Style="{StaticResource BodyTextStyle}"
                          FontSize="10"
                          HorizontalAlignment="Center"
                          Foreground="{StaticResource TextSecondaryBrush}"/>
            </StackPanel>

            <!-- 底部按钮区域 -->
            <StackPanel Grid.Row="4" 
                       Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Margin="20">
                
                <Button x:Name="SkipButton"
                       Content="跳过"
                       Style="{StaticResource SecondaryButtonStyle}"
                       Width="80"
                       Height="32"
                       FontSize="12"
                       Margin="8,0"
                       Click="SkipButton_Click"/>

                <Button x:Name="SettingsButton"
                       Style="{StaticResource IconButtonStyle}"
                       Width="32"
                       Height="32"
                       Margin="8,0"
                       ToolTip="设置"
                       Click="SettingsButton_Click">
                    <materialDesign:PackIcon Kind="Settings" Width="16" Height="16"/>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>
