using System.Windows;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using CursorResetTool.Commercial.Services;
using CursorResetTool.Commercial.Views;
using CursorResetTool.Commercial.ViewModels;
using Serilog;

namespace CursorResetTool.Commercial
{
    public partial class App : Application
    {
        private IHost? _host;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // 配置日志
            Log.Logger = new LoggerConfiguration()
                .WriteTo.File("logs/app-.log", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            // 设置全局异常处理
            this.DispatcherUnhandledException += OnDispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

            // 构建主机和服务容器
            _host = CreateHostBuilder().Build();

            // 检查管理员权限
            var securityService = _host.Services.GetRequiredService<ISecurityService>();
            if (!securityService.IsRunAsAdministrator())
            {
                MessageBox.Show(
                    "此程序需要管理员权限才能正常运行。\n请右键点击程序，选择"以管理员身份运行"。",
                    "尚书_CAR_cursor重置系统 - 权限不足",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);

                Current.Shutdown();
                return;
            }

            // 启动主机
            _host.Start();

            // 启动启动画面
            var splashWindow = _host.Services.GetRequiredService<SplashWindow>();
            splashWindow.Show();
        }

        private IHostBuilder CreateHostBuilder()
        {
            return Host.CreateDefaultBuilder()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: true);
                })
                .ConfigureServices((context, services) =>
                {
                    // 注册服务
                    services.AddSingleton<ISecurityService, SecurityService>();
                    services.AddSingleton<ILicenseService, LicenseService>();
                    services.AddSingleton<IUserService, UserService>();
                    services.AddSingleton<IMachineCodeService, MachineCodeService>();
                    services.AddSingleton<INetworkService, NetworkService>();
                    services.AddSingleton<ICursorResetService, CursorResetService>();
                    services.AddSingleton<ILocalizationService, LocalizationService>();
                    services.AddSingleton<IUpdateService, UpdateService>();

                    // 注册视图和视图模型
                    services.AddTransient<SplashWindow>();
                    services.AddTransient<LoginWindow>();
                    services.AddTransient<RegisterWindow>();
                    services.AddTransient<MainWindow>();
                    services.AddTransient<SettingsWindow>();

                    services.AddTransient<SplashViewModel>();
                    services.AddTransient<LoginViewModel>();
                    services.AddTransient<RegisterViewModel>();
                    services.AddTransient<MainViewModel>();
                    services.AddTransient<SettingsViewModel>();
                });
        }

        private void OnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            Log.Fatal(e.Exception, "Unhandled UI exception");

            MessageBox.Show(
                $"程序发生未处理的错误：\n{e.Exception.Message}\n\n错误详情已记录到日志文件中。",
                "尚书_CAR_cursor重置系统 - 程序错误",
                MessageBoxButton.OK,
                MessageBoxImage.Error);

            e.Handled = true;
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                Log.Fatal(ex, "Unhandled application exception");
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            Log.Information("Application shutting down");
            _host?.Dispose();
            Log.CloseAndFlush();
            base.OnExit(e);
        }
    }
}
