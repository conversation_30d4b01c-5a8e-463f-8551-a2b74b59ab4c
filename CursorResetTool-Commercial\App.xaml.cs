using System.Windows;
using System.Windows.Threading;
using CursorResetTool.Commercial.Services;
using CursorResetTool.Commercial.Views;

namespace CursorResetTool.Commercial
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // 设置全局异常处理
            this.DispatcherUnhandledException += OnDispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

            // 初始化服务
            ServiceContainer.Initialize();

            // 检查管理员权限
            if (!SecurityService.IsRunAsAdministrator())
            {
                MessageBox.Show(
                    "此程序需要管理员权限才能正常运行。\n请右键点击程序，选择"以管理员身份运行"。",
                    "权限不足",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                
                Current.Shutdown();
                return;
            }

            // 启动主窗口
            var splashWindow = new SplashWindow();
            splashWindow.Show();
        }

        private void OnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            LoggingService.LogCritical("Unhandled UI exception", e.Exception);
            
            MessageBox.Show(
                $"程序发生未处理的错误：\n{e.Exception.Message}\n\n错误详情已记录到日志文件中。",
                "程序错误",
                MessageBoxButton.OK,
                MessageBoxImage.Error);

            e.Handled = true;
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                LoggingService.LogCritical("Unhandled application exception", ex);
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            LoggingService.LogApplicationEnd();
            base.OnExit(e);
        }
    }
}
