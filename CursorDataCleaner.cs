using System;
using System.IO;
using System.Collections.Generic;
using Microsoft.Win32;
using System.Diagnostics;

namespace CursorReset
{
    public class CursorDataCleaner
    {
        public void CleanAll()
        {
            Console.WriteLine("🧹 开始清理 Cursor 数据...");
            Console.WriteLine("⚠️ 请确保已经备份重要数据！");
            Console.WriteLine();

            // 首先尝试关闭 Cursor 进程
            CloseProcesses();

            // 扫描现有数据
            var scanner = new CursorDataScanner();
            scanner.ScanAll();

            var foundFiles = scanner.GetFoundFiles();
            var foundRegistryKeys = scanner.GetFoundRegistryKeys();

            if (foundFiles.Count == 0 && foundRegistryKeys.Count == 0)
            {
                Console.WriteLine("✅ 没有找到需要清理的数据。");
                return;
            }

            Console.WriteLine($"\n即将清理 {foundFiles.Count} 个文件/目录和 {foundRegistryKeys.Count} 个注册表项");
            Console.WriteLine("最后确认：输入 'CONFIRM' 继续清理操作：");
            
            if (Console.ReadLine() != "CONFIRM")
            {
                Console.WriteLine("❌ 清理操作已取消。");
                return;
            }

            // 清理文件和目录
            CleanFiles(foundFiles);

            // 清理注册表
            CleanRegistry(foundRegistryKeys);

            // 清理其他相关项
            CleanAdditionalItems();

            Console.WriteLine("\n✅ 清理完成！");
            Console.WriteLine("💡 建议重启计算机以确保所有更改生效。");
        }

        private void CloseProcesses()
        {
            Console.WriteLine("🔄 尝试关闭 Cursor 相关进程...");
            
            var processNames = new[] { "Cursor", "cursor", "cursor-updater" };
            
            foreach (var processName in processNames)
            {
                try
                {
                    var processes = Process.GetProcessesByName(processName);
                    foreach (var process in processes)
                    {
                        try
                        {
                            Console.WriteLine($"🔄 关闭进程: {process.ProcessName} (PID: {process.Id})");
                            process.CloseMainWindow();
                            
                            // 等待进程优雅关闭
                            if (!process.WaitForExit(5000))
                            {
                                Console.WriteLine($"⚠️ 强制终止进程: {process.ProcessName}");
                                process.Kill();
                            }
                            
                            Console.WriteLine($"✅ 进程已关闭: {process.ProcessName}");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"❌ 关闭进程失败 {process.ProcessName}: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 查找进程失败 {processName}: {ex.Message}");
                }
            }
            
            // 等待一下确保进程完全关闭
            System.Threading.Thread.Sleep(2000);
        }

        private void CleanFiles(List<string> filePaths)
        {
            Console.WriteLine("\n🗂️ 清理文件和目录...");
            
            foreach (var path in filePaths)
            {
                try
                {
                    if (Directory.Exists(path))
                    {
                        Console.WriteLine($"🗂️ 删除目录: {path}");
                        Directory.Delete(path, true);
                        Console.WriteLine($"✅ 目录已删除: {path}");
                    }
                    else if (File.Exists(path))
                    {
                        Console.WriteLine($"📄 删除文件: {path}");
                        
                        // 移除只读属性
                        var attributes = File.GetAttributes(path);
                        if ((attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly)
                        {
                            File.SetAttributes(path, attributes & ~FileAttributes.ReadOnly);
                        }
                        
                        File.Delete(path);
                        Console.WriteLine($"✅ 文件已删除: {path}");
                    }
                }
                catch (UnauthorizedAccessException)
                {
                    Console.WriteLine($"❌ 权限不足，无法删除: {path}");
                    TryForceDelete(path);
                }
                catch (IOException ex)
                {
                    Console.WriteLine($"❌ 文件被占用，无法删除: {path} - {ex.Message}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 删除失败 {path}: {ex.Message}");
                }
            }
        }

        private void TryForceDelete(string path)
        {
            try
            {
                Console.WriteLine($"🔧 尝试强制删除: {path}");
                
                if (Directory.Exists(path))
                {
                    // 递归移除只读属性
                    var dirInfo = new DirectoryInfo(path);
                    SetAttributesNormal(dirInfo);
                    Directory.Delete(path, true);
                }
                else if (File.Exists(path))
                {
                    File.SetAttributes(path, FileAttributes.Normal);
                    File.Delete(path);
                }
                
                Console.WriteLine($"✅ 强制删除成功: {path}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 强制删除失败 {path}: {ex.Message}");
            }
        }

        private void SetAttributesNormal(DirectoryInfo dir)
        {
            foreach (var subDir in dir.GetDirectories())
            {
                SetAttributesNormal(subDir);
            }
            
            foreach (var file in dir.GetFiles())
            {
                file.Attributes = FileAttributes.Normal;
            }
            
            dir.Attributes = FileAttributes.Normal;
        }

        private void CleanRegistry(List<string> registryKeys)
        {
            Console.WriteLine("\n🗃️ 清理注册表...");
            
            foreach (var keyPath in registryKeys)
            {
                try
                {
                    Console.WriteLine($"🗃️ 删除注册表项: HKEY_CURRENT_USER\\{keyPath}");
                    
                    using var parentKey = GetParentKey(keyPath);
                    if (parentKey != null)
                    {
                        var keyName = GetKeyName(keyPath);
                        parentKey.DeleteSubKeyTree(keyName, false);
                        Console.WriteLine($"✅ 注册表项已删除: {keyPath}");
                    }
                    else
                    {
                        Console.WriteLine($"❌ 找不到父注册表项: {keyPath}");
                    }
                }
                catch (ArgumentException)
                {
                    Console.WriteLine($"ℹ️ 注册表项不存在: {keyPath}");
                }
                catch (UnauthorizedAccessException)
                {
                    Console.WriteLine($"❌ 权限不足，无法删除注册表项: {keyPath}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 删除注册表项失败 {keyPath}: {ex.Message}");
                }
            }
        }

        private RegistryKey? GetParentKey(string keyPath)
        {
            var lastSlash = keyPath.LastIndexOf('\\');
            if (lastSlash == -1)
            {
                return Registry.CurrentUser;
            }
            
            var parentPath = keyPath.Substring(0, lastSlash);
            return Registry.CurrentUser.OpenSubKey(parentPath, true);
        }

        private string GetKeyName(string keyPath)
        {
            var lastSlash = keyPath.LastIndexOf('\\');
            return lastSlash == -1 ? keyPath : keyPath.Substring(lastSlash + 1);
        }

        private void CleanAdditionalItems()
        {
            Console.WriteLine("\n🧹 清理其他相关项...");
            
            // 清理临时文件
            CleanTempFiles();
            
            // 清理最近使用的文件列表
            CleanRecentFiles();
            
            // 清理系统缓存
            CleanSystemCache();
        }

        private void CleanTempFiles()
        {
            try
            {
                var tempPath = Path.GetTempPath();
                var cursorTempFiles = Directory.GetFiles(tempPath, "*cursor*", SearchOption.TopDirectoryOnly);
                
                foreach (var file in cursorTempFiles)
                {
                    try
                    {
                        File.Delete(file);
                        Console.WriteLine($"✅ 删除临时文件: {file}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ 删除临时文件失败 {file}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 清理临时文件失败: {ex.Message}");
            }
        }

        private void CleanRecentFiles()
        {
            try
            {
                var recentPath = Environment.GetFolderPath(Environment.SpecialFolder.Recent);
                var cursorLinks = Directory.GetFiles(recentPath, "*cursor*", SearchOption.TopDirectoryOnly);
                
                foreach (var link in cursorLinks)
                {
                    try
                    {
                        File.Delete(link);
                        Console.WriteLine($"✅ 删除最近文件链接: {link}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ 删除最近文件链接失败 {link}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 清理最近文件失败: {ex.Message}");
            }
        }

        private void CleanSystemCache()
        {
            Console.WriteLine("🧹 清理系统缓存...");
            
            try
            {
                // 清理图标缓存
                var iconCachePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "IconCache.db");
                if (File.Exists(iconCachePath))
                {
                    try
                    {
                        File.Delete(iconCachePath);
                        Console.WriteLine("✅ 清理图标缓存");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ 清理图标缓存失败: {ex.Message}");
                    }
                }
                
                Console.WriteLine("💡 建议手动清理浏览器缓存和系统预取文件");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 清理系统缓存失败: {ex.Message}");
            }
        }
    }
}
