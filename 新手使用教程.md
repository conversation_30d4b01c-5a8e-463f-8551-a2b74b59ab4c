# Cursor Reset Tool Pro - 新手使用教程

## 🎯 软件简介

**Cursor Reset Tool Pro** 是一款专业的 Cursor 编辑器重置工具，具备完整的许可证管理系统。软件可以安全地清理 Cursor 编辑器的所有数据，让您的 Cursor 恢复到全新安装状态。

### 主要功能
- 🔍 **智能扫描** - 自动识别 Cursor 相关文件和注册表
- 💾 **安全备份** - 清理前自动备份重要数据
- 🧹 **彻底清理** - 完全移除 Cursor 相关数据
- 🔄 **数据恢复** - 从备份恢复数据
- 🔐 **许可证管理** - 专业的软件授权系统

## 📦 获取软件

### 软件包内容
下载后您会得到以下文件：
```
CursorResetTool-Release/
├── CursorResetTool-x64.exe     ← 64位系统使用
├── CursorResetTool-x86.exe     ← 32位系统使用
├── 运行工具-x64.bat            ← 64位启动脚本
├── 运行工具-x86.bat            ← 32位启动脚本
├── 许可证说明.txt              ← 测试许可证密钥
├── 使用说明.txt                ← 简化使用指南
└── README.md                   ← 详细说明文档
```

### 系统要求
- **操作系统**: Windows 10/11 (64位推荐)
- **权限要求**: 管理员权限
- **磁盘空间**: 至少 50MB 可用空间
- **网络**: 激活时需要网络连接 (可选)

## 🚀 快速开始

### 方法一：使用启动脚本 (推荐)

1. **选择对应版本**
   - 64位系统：双击 `运行工具-x64.bat`
   - 32位系统：双击 `运行工具-x86.bat`

2. **确认管理员权限**
   - 系统会弹出 UAC 提示
   - 点击"是"授予管理员权限

3. **程序自动启动**
   - 脚本会自动检查环境
   - 以管理员身份启动程序

### 方法二：直接运行程序

1. **右键点击程序文件**
   - 64位系统：右键 `CursorResetTool-x64.exe`
   - 32位系统：右键 `CursorResetTool-x86.exe`

2. **选择"以管理员身份运行"**

3. **确认 UAC 提示**
   - 点击"是"继续

## 🔐 首次使用 - 许可证激活

### 启动界面
程序启动后会显示：
```
=== Cursor Reset Tool Pro (Licensed Version) ===
警告：此工具仅用于学习目的！
Warning: This tool is for educational purposes only!

✅ 检测到管理员权限

🔍 检查许可证状态...
❌ 许可证无效或试用期已过期

请选择：
1. 激活许可证
2. 离线激活  
3. 申请试用
4. 退出程序
请选择 (1-4): 
```

### 选项说明

#### 选项1：在线激活许可证
**适用场景**: 有网络连接，拥有正式许可证密钥

1. 输入 `1` 并按回车
2. 进入许可证管理界面
3. 选择 `1. 激活许可证`
4. 输入许可证密钥 (例如: `DEMO-1234-5678-9ABC`)
5. 输入邮箱地址 (例如: `<EMAIL>`)
6. 等待激活完成

**激活过程示例**:
```
=== 在线激活许可证 ===

请输入许可证密钥: DEMO-1234-5678-9ABC
请输入邮箱地址: <EMAIL>

🔄 正在激活许可证...
🔄 尝试连接许可证服务器...
✅ 服务器激活成功
✅ 许可证激活成功！
许可证类型: 专业版
到期日期: 2025-01-15
```

#### 选项2：离线激活
**适用场景**: 无网络连接或网络激活失败

1. 输入 `2` 并按回车
2. 选择 `2. 离线激活`
3. 输入许可证密钥
4. 输入邮箱地址
5. 系统会进行本地验证

#### 选项3：申请试用 (推荐新手)
**适用场景**: 首次使用，想要体验软件功能

1. 输入 `3` 并按回车
2. 选择 `3. 申请试用`
3. 系统自动生成7天试用许可证
4. 可以使用所有功能

**试用激活示例**:
```
=== 申请试用许可证 ===

🔄 正在生成试用许可证...
✅ 试用许可证生成成功！
试用密钥: TRIAL-XXXX-XXXX-XXXX
试用期限: 7 天
💡 试用期间可以使用所有功能
```

### 测试许可证密钥

软件包含以下测试密钥供学习使用：

| 许可证密钥 | 类型 | 激活数 | 说明 |
|------------|------|--------|------|
| `DEMO-1234-5678-9ABC` | 专业版 | 3台设备 | 功能完整 |
| `TEST-ABCD-EFGH-IJKL` | 个人版 | 1台设备 | 基础功能 |
| `ENTERPRISE-2024-FULL` | 企业版 | 100台设备 | 所有功能 |

## 🎮 主要功能使用

### 激活成功后的主界面
```
=== Cursor Reset Tool Pro ===
✅ 已授权 | 用户: <EMAIL> | 类型: 专业版
⏰ 许可证剩余: 365 天

请选择操作：
1. 扫描 Cursor 相关文件和注册表
2. 备份 Cursor 数据
3. 清理 Cursor 数据 (谨慎操作!)
4. 恢复备份
5. 许可证管理
6. 查看日志
7. 退出
请输入选项 (1-7): 
```

### 功能详细说明

#### 1. 扫描 Cursor 数据
**作用**: 检查系统中的 Cursor 相关文件和注册表项

**使用步骤**:
1. 输入 `1` 并按回车
2. 程序自动扫描系统
3. 显示找到的文件和注册表项
4. 显示占用的磁盘空间

**扫描结果示例**:
```
🔍 扫描 Cursor 相关数据...

📁 扫描文件系统...
✅ 找到: Cursor 应用数据
   路径: C:\Users\<USER>\AppData\Roaming\Cursor
   大小: 15.2 MB

✅ 找到: Cursor 本地数据  
   路径: C:\Users\<USER>\AppData\Local\Cursor
   大小: 128.5 MB

🗃️ 扫描注册表...
✅ 找到注册表项: Cursor 主配置
   路径: HKEY_CURRENT_USER\Software\Cursor

📊 扫描结果汇总:
📁 找到 3 个文件/目录
🗃️ 找到 2 个注册表项
```

#### 2. 备份 Cursor 数据 (重要!)
**作用**: 在清理前备份所有 Cursor 数据，以防需要恢复

**使用步骤**:
1. 输入 `2` 并按回车
2. 程序自动创建备份
3. 备份文件保存到桌面

**备份过程示例**:
```
💾 备份 Cursor 数据...

🔍 扫描需要备份的数据...
💾 创建备份文件: C:\Users\<USER>\Desktop\CursorBackup\CursorBackup_20241219_103000.zip

📁 备份目录: C:\Users\<USER>\AppData\Roaming\Cursor
📄 备份文件: C:\Users\<USER>\Desktop\Cursor.lnk
🗃️ 备份注册表...

✅ 备份完成！
📁 备份文件位置: C:\Users\<USER>\Desktop\CursorBackup\CursorBackup_20241219_103000.zip
```

#### 3. 清理 Cursor 数据 (核心功能)
**作用**: 完全清理 Cursor 相关数据，恢复到全新状态

**⚠️ 重要提醒**: 
- 清理前务必先备份数据
- 清理操作不可逆转
- 会删除所有 Cursor 配置和数据

**使用步骤**:
1. **先执行备份** (选项2)
2. 输入 `3` 并按回车
3. 阅读警告信息
4. 输入 `YES` 确认第一次
5. 输入 `CONFIRM` 确认第二次
6. 等待清理完成

**清理过程示例**:
```
⚠️  警告：即将清理 Cursor 数据！
这将删除所有 Cursor 相关的配置、缓存和注册表项。
确定要继续吗？(输入 'YES' 确认): YES

🧹 开始清理 Cursor 数据...
⚠️ 请确保已经备份重要数据！

即将清理 3 个文件/目录和 2 个注册表项
最后确认：输入 'CONFIRM' 继续清理操作：CONFIRM

🔄 尝试关闭 Cursor 相关进程...
✅ 进程已关闭: Cursor

🗂️ 清理文件和目录...
✅ 目录已删除: C:\Users\<USER>\AppData\Roaming\Cursor

🗃️ 清理注册表...
✅ 注册表项已删除: HKEY_CURRENT_USER\Software\Cursor

✅ 清理完成！
💡 建议重启计算机以确保所有更改生效。
```

#### 4. 恢复备份
**作用**: 从之前的备份文件恢复 Cursor 数据

**使用步骤**:
1. 输入 `4` 并按回车
2. 选择要恢复的备份文件
3. 确认恢复操作

#### 5. 许可证管理
**作用**: 管理软件许可证，查看状态，重新激活等

**功能包括**:
- 查看许可证详细信息
- 重新激活许可证
- 移除许可证
- 在线验证状态

#### 6. 查看日志
**作用**: 查看程序运行日志，排查问题

**功能包括**:
- 查看今日日志
- 打开日志目录
- 清理旧日志
- 测试日志功能

## 🔧 常见问题解决

### 问题1: 程序无法启动
**症状**: 双击程序没有反应

**解决方案**:
1. 确保以管理员身份运行
2. 检查是否被杀毒软件拦截
3. 尝试使用启动脚本 (.bat 文件)

### 问题2: 许可证激活失败
**症状**: 显示"激活失败"或"网络错误"

**解决方案**:
1. 检查网络连接
2. 尝试离线激活
3. 使用试用许可证
4. 检查许可证密钥是否正确

### 问题3: 清理后 Cursor 仍有残留
**症状**: 清理后 Cursor 还有一些设置保留

**解决方案**:
1. 重启计算机
2. 手动检查以下位置：
   - `%APPDATA%\Cursor`
   - `%LOCALAPPDATA%\Cursor`
   - 注册表 `HKEY_CURRENT_USER\Software\Cursor`

### 问题4: 备份文件找不到
**症状**: 备份完成但找不到备份文件

**解决方案**:
1. 检查桌面的 `CursorBackup` 文件夹
2. 使用程序的"查看日志"功能查看备份路径
3. 搜索文件名格式：`CursorBackup_*.zip`

## 📞 技术支持

### 日志文件位置
如果遇到问题，可以查看日志文件：
- 位置：`%APPDATA%\CursorResetTool\logs\`
- 文件名：`app_YYYYMMDD.log`

### 联系支持
如果遇到无法解决的问题：
1. 使用程序的"查看日志"功能
2. 复制错误信息
3. 联系技术支持并提供日志信息

## ⚠️ 重要提醒

1. **备份重要**: 清理前务必备份数据
2. **管理员权限**: 程序必须以管理员身份运行
3. **关闭 Cursor**: 清理前请关闭 Cursor 程序
4. **重启建议**: 清理完成后建议重启计算机
5. **学习目的**: 本软件仅用于学习和研究目的

## 🎯 使用流程总结

### 标准使用流程
```
1. 以管理员身份启动程序
   ↓
2. 激活许可证 (试用/正式)
   ↓  
3. 扫描 Cursor 数据
   ↓
4. 备份 Cursor 数据 (重要!)
   ↓
5. 清理 Cursor 数据
   ↓
6. 重启计算机
   ↓
7. 重新安装 Cursor (如需要)
```

### 快速上手 (新手推荐)
1. 双击 `运行工具-x64.bat`
2. 选择 `3` (申请试用)
3. 选择 `1` (扫描数据)
4. 选择 `2` (备份数据)
5. 选择 `3` (清理数据)

---

**祝您使用愉快！如有问题，请查看日志文件或联系技术支持。**
