using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Text;
using Microsoft.Win32;

namespace CursorResetTool.Commercial.Services
{
    /// <summary>
    /// Cursor重置服务接口
    /// </summary>
    public interface ICursorResetService
    {
        /// <summary>
        /// 扫描Cursor相关数据
        /// </summary>
        Task<ScanResult> ScanCursorDataAsync();

        /// <summary>
        /// 备份Cursor数据
        /// </summary>
        Task<(bool success, string backupPath, string message)> BackupCursorDataAsync(IProgress<int>? progress = null);

        /// <summary>
        /// 清理Cursor数据
        /// </summary>
        Task<(bool success, string message)> CleanCursorDataAsync(IProgress<int>? progress = null);

        /// <summary>
        /// 恢复Cursor数据
        /// </summary>
        Task<(bool success, string message)> RestoreCursorDataAsync(string backupPath, IProgress<int>? progress = null);

        /// <summary>
        /// 获取备份列表
        /// </summary>
        Task<List<BackupInfo>> GetBackupListAsync();

        /// <summary>
        /// 删除备份
        /// </summary>
        Task<bool> DeleteBackupAsync(string backupPath);

        /// <summary>
        /// 检查Cursor进程
        /// </summary>
        Task<List<Process>> GetCursorProcessesAsync();

        /// <summary>
        /// 关闭Cursor进程
        /// </summary>
        Task<(bool success, string message)> CloseCursorProcessesAsync();
    }

    /// <summary>
    /// 扫描结果
    /// </summary>
    public class ScanResult
    {
        public List<FileSystemItem> Files { get; set; } = new();
        public List<RegistryItem> RegistryItems { get; set; } = new();
        public long TotalSize { get; set; }
        public int TotalItems => Files.Count + RegistryItems.Count;
    }

    /// <summary>
    /// 文件系统项
    /// </summary>
    public class FileSystemItem
    {
        public string Path { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // File, Directory
        public long Size { get; set; }
        public DateTime LastModified { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 注册表项
    /// </summary>
    public class RegistryItem
    {
        public string Path { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // Key, Value
        public string Description { get; set; } = string.Empty;
        public int SubKeyCount { get; set; }
        public int ValueCount { get; set; }
    }

    /// <summary>
    /// 备份信息
    /// </summary>
    public class BackupInfo
    {
        public string FilePath { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public DateTime CreateTime { get; set; }
        public long FileSize { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// Cursor重置服务实现
    /// </summary>
    public class CursorResetService : ICursorResetService
    {
        private readonly string _backupDirectory;
        private readonly List<string> _cursorPaths;
        private readonly List<string> _cursorRegistryPaths;

        public CursorResetService()
        {
            _backupDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "CursorBackup");
            Directory.CreateDirectory(_backupDirectory);

            // 定义Cursor相关路径
            _cursorPaths = new List<string>
            {
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Cursor"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Cursor"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "cursor-updater"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), ".cursor"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "Cursor.lnk"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.StartMenu), "Programs", "Cursor.lnk"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonStartMenu), "Programs", "Cursor.lnk")
            };

            _cursorRegistryPaths = new List<string>
            {
                @"HKEY_CURRENT_USER\Software\Cursor",
                @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Uninstall\Cursor",
                @"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Cursor",
                @"HKEY_CURRENT_USER\Software\Classes\cursor",
                @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\ApplicationAssociationToasts"
            };
        }

        public async Task<ScanResult> ScanCursorDataAsync()
        {
            var result = new ScanResult();

            await Task.Run(() =>
            {
                // 扫描文件系统
                foreach (var path in _cursorPaths)
                {
                    try
                    {
                        if (File.Exists(path))
                        {
                            var fileInfo = new FileInfo(path);
                            result.Files.Add(new FileSystemItem
                            {
                                Path = path,
                                Type = "File",
                                Size = fileInfo.Length,
                                LastModified = fileInfo.LastWriteTime,
                                Description = GetFileDescription(path)
                            });
                            result.TotalSize += fileInfo.Length;
                        }
                        else if (Directory.Exists(path))
                        {
                            var dirInfo = new DirectoryInfo(path);
                            var size = GetDirectorySize(dirInfo);
                            result.Files.Add(new FileSystemItem
                            {
                                Path = path,
                                Type = "Directory",
                                Size = size,
                                LastModified = dirInfo.LastWriteTime,
                                Description = GetDirectoryDescription(path)
                            });
                            result.TotalSize += size;
                        }
                    }
                    catch (Exception ex)
                    {
                        Serilog.Log.Warning(ex, "Failed to scan path: {Path}", path);
                    }
                }

                // 扫描注册表
                foreach (var regPath in _cursorRegistryPaths)
                {
                    try
                    {
                        var registryItem = ScanRegistryPath(regPath);
                        if (registryItem != null)
                        {
                            result.RegistryItems.Add(registryItem);
                        }
                    }
                    catch (Exception ex)
                    {
                        Serilog.Log.Warning(ex, "Failed to scan registry path: {Path}", regPath);
                    }
                }
            });

            return result;
        }

        public async Task<(bool success, string backupPath, string message)> BackupCursorDataAsync(IProgress<int>? progress = null)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"CursorBackup_{timestamp}.zip";
                var backupPath = Path.Combine(_backupDirectory, backupFileName);

                progress?.Report(10);

                // 扫描数据
                var scanResult = await ScanCursorDataAsync();
                progress?.Report(20);

                if (scanResult.TotalItems == 0)
                {
                    return (false, "", "未找到需要备份的Cursor数据");
                }

                // 创建备份
                using var archive = ZipFile.Open(backupPath, ZipArchiveMode.Create);
                
                var totalItems = scanResult.Files.Count + scanResult.RegistryItems.Count;
                var processedItems = 0;

                // 备份文件
                foreach (var file in scanResult.Files)
                {
                    try
                    {
                        if (file.Type == "File" && File.Exists(file.Path))
                        {
                            var entryName = GetRelativeBackupPath(file.Path);
                            archive.CreateEntryFromFile(file.Path, entryName);
                        }
                        else if (file.Type == "Directory" && Directory.Exists(file.Path))
                        {
                            await BackupDirectory(archive, file.Path);
                        }

                        processedItems++;
                        var progressValue = 20 + (processedItems * 60 / totalItems);
                        progress?.Report(progressValue);
                    }
                    catch (Exception ex)
                    {
                        Serilog.Log.Warning(ex, "Failed to backup file: {Path}", file.Path);
                    }
                }

                // 备份注册表
                await BackupRegistry(archive, scanResult.RegistryItems);
                progress?.Report(90);

                // 创建备份信息文件
                var backupInfo = new
                {
                    BackupTime = DateTime.Now,
                    TotalFiles = scanResult.Files.Count,
                    TotalRegistryItems = scanResult.RegistryItems.Count,
                    TotalSize = scanResult.TotalSize,
                    Files = scanResult.Files,
                    RegistryItems = scanResult.RegistryItems
                };

                var infoJson = Newtonsoft.Json.JsonConvert.SerializeObject(backupInfo, Newtonsoft.Json.Formatting.Indented);
                var infoEntry = archive.CreateEntry("backup_info.json");
                using var infoStream = infoEntry.Open();
                using var writer = new StreamWriter(infoStream);
                await writer.WriteAsync(infoJson);

                progress?.Report(100);

                return (true, backupPath, $"备份完成！\n备份文件：{backupFileName}\n备份项目：{scanResult.TotalItems} 个");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Backup failed");
                return (false, "", $"备份失败：{ex.Message}");
            }
        }

        public async Task<(bool success, string message)> CleanCursorDataAsync(IProgress<int>? progress = null)
        {
            try
            {
                progress?.Report(5);

                // 关闭Cursor进程
                var closeResult = await CloseCursorProcessesAsync();
                if (!closeResult.success)
                {
                    return (false, closeResult.message);
                }

                progress?.Report(15);

                // 扫描数据
                var scanResult = await ScanCursorDataAsync();
                progress?.Report(25);

                if (scanResult.TotalItems == 0)
                {
                    return (true, "未找到需要清理的Cursor数据");
                }

                var totalItems = scanResult.Files.Count + scanResult.RegistryItems.Count;
                var processedItems = 0;

                // 清理文件
                foreach (var file in scanResult.Files)
                {
                    try
                    {
                        if (file.Type == "File" && File.Exists(file.Path))
                        {
                            File.Delete(file.Path);
                        }
                        else if (file.Type == "Directory" && Directory.Exists(file.Path))
                        {
                            Directory.Delete(file.Path, true);
                        }

                        processedItems++;
                        var progressValue = 25 + (processedItems * 50 / totalItems);
                        progress?.Report(progressValue);
                    }
                    catch (Exception ex)
                    {
                        Serilog.Log.Warning(ex, "Failed to delete file: {Path}", file.Path);
                    }
                }

                // 清理注册表
                foreach (var regItem in scanResult.RegistryItems)
                {
                    try
                    {
                        DeleteRegistryPath(regItem.Path);
                        processedItems++;
                        var progressValue = 25 + (processedItems * 50 / totalItems);
                        progress?.Report(progressValue);
                    }
                    catch (Exception ex)
                    {
                        Serilog.Log.Warning(ex, "Failed to delete registry: {Path}", regItem.Path);
                    }
                }

                // 清理临时文件
                await CleanTempFiles();
                progress?.Report(90);

                // 清理最近文件记录
                await CleanRecentFiles();
                progress?.Report(100);

                return (true, $"清理完成！\n已清理 {scanResult.TotalItems} 个项目");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Clean failed");
                return (false, $"清理失败：{ex.Message}");
            }
        }

        public async Task<(bool success, string message)> RestoreCursorDataAsync(string backupPath, IProgress<int>? progress = null)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    return (false, "备份文件不存在");
                }

                progress?.Report(10);

                using var archive = ZipFile.OpenRead(backupPath);
                var totalEntries = archive.Entries.Count;
                var processedEntries = 0;

                foreach (var entry in archive.Entries)
                {
                    try
                    {
                        if (entry.Name == "backup_info.json" || entry.Name.EndsWith(".reg"))
                        {
                            // 跳过信息文件和注册表文件，这些需要特殊处理
                            continue;
                        }

                        var destinationPath = GetAbsoluteRestorePath(entry.FullName);
                        var destinationDir = Path.GetDirectoryName(destinationPath);
                        
                        if (!string.IsNullOrEmpty(destinationDir))
                        {
                            Directory.CreateDirectory(destinationDir);
                        }

                        entry.ExtractToFile(destinationPath, true);

                        processedEntries++;
                        var progressValue = 10 + (processedEntries * 80 / totalEntries);
                        progress?.Report(progressValue);
                    }
                    catch (Exception ex)
                    {
                        Serilog.Log.Warning(ex, "Failed to restore entry: {Entry}", entry.FullName);
                    }
                }

                // 恢复注册表
                await RestoreRegistry(archive);
                progress?.Report(100);

                return (true, "恢复完成！");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Restore failed");
                return (false, $"恢复失败：{ex.Message}");
            }
        }

        public async Task<List<BackupInfo>> GetBackupListAsync()
        {
            var backups = new List<BackupInfo>();

            await Task.Run(() =>
            {
                try
                {
                    var backupFiles = Directory.GetFiles(_backupDirectory, "CursorBackup_*.zip");
                    
                    foreach (var file in backupFiles)
                    {
                        var fileInfo = new FileInfo(file);
                        backups.Add(new BackupInfo
                        {
                            FilePath = file,
                            FileName = fileInfo.Name,
                            CreateTime = fileInfo.CreationTime,
                            FileSize = fileInfo.Length,
                            Description = GetBackupDescription(file)
                        });
                    }

                    // 按创建时间倒序排列
                    backups = backups.OrderByDescending(b => b.CreateTime).ToList();
                }
                catch (Exception ex)
                {
                    Serilog.Log.Error(ex, "Failed to get backup list");
                }
            });

            return backups;
        }

        public async Task<bool> DeleteBackupAsync(string backupPath)
        {
            try
            {
                if (File.Exists(backupPath))
                {
                    await Task.Run(() => File.Delete(backupPath));
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to delete backup: {Path}", backupPath);
                return false;
            }
        }

        public async Task<List<Process>> GetCursorProcessesAsync()
        {
            return await Task.Run(() =>
            {
                var processes = new List<Process>();
                try
                {
                    var cursorProcesses = Process.GetProcessesByName("Cursor");
                    processes.AddRange(cursorProcesses);

                    var codeProcesses = Process.GetProcessesByName("Code");
                    foreach (var process in codeProcesses)
                    {
                        try
                        {
                            if (process.MainModule?.FileName?.Contains("Cursor") == true)
                            {
                                processes.Add(process);
                            }
                        }
                        catch
                        {
                            // 忽略访问被拒绝的进程
                        }
                    }
                }
                catch (Exception ex)
                {
                    Serilog.Log.Error(ex, "Failed to get Cursor processes");
                }
                return processes;
            });
        }

        public async Task<(bool success, string message)> CloseCursorProcessesAsync()
        {
            try
            {
                var processes = await GetCursorProcessesAsync();
                
                if (processes.Count == 0)
                {
                    return (true, "未发现运行中的Cursor进程");
                }

                var closedCount = 0;
                foreach (var process in processes)
                {
                    try
                    {
                        process.CloseMainWindow();
                        if (!process.WaitForExit(5000)) // 等待5秒
                        {
                            process.Kill(); // 强制结束
                        }
                        closedCount++;
                    }
                    catch (Exception ex)
                    {
                        Serilog.Log.Warning(ex, "Failed to close process: {ProcessName}", process.ProcessName);
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                return (true, $"已关闭 {closedCount} 个Cursor进程");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to close Cursor processes");
                return (false, "关闭进程失败");
            }
        }

        private long GetDirectorySize(DirectoryInfo directory)
        {
            try
            {
                return directory.GetFiles("*", SearchOption.AllDirectories).Sum(file => file.Length);
            }
            catch
            {
                return 0;
            }
        }

        private string GetFileDescription(string path)
        {
            var fileName = Path.GetFileName(path);
            return fileName switch
            {
                "Cursor.lnk" => "Cursor桌面快捷方式",
                _ => "Cursor相关文件"
            };
        }

        private string GetDirectoryDescription(string path)
        {
            var dirName = Path.GetFileName(path);
            return dirName switch
            {
                "Cursor" => "Cursor主要数据目录",
                "cursor-updater" => "Cursor更新程序目录",
                ".cursor" => "Cursor用户配置目录",
                _ => "Cursor相关目录"
            };
        }

        private RegistryItem? ScanRegistryPath(string path)
        {
            try
            {
                var parts = path.Split('\\');
                if (parts.Length < 2) return null;

                var hive = parts[0] switch
                {
                    "HKEY_CURRENT_USER" => Registry.CurrentUser,
                    "HKEY_LOCAL_MACHINE" => Registry.LocalMachine,
                    "HKEY_CLASSES_ROOT" => Registry.ClassesRoot,
                    _ => null
                };

                if (hive == null) return null;

                var keyPath = string.Join("\\", parts.Skip(1));
                using var key = hive.OpenSubKey(keyPath);
                
                if (key != null)
                {
                    return new RegistryItem
                    {
                        Path = path,
                        Type = "Key",
                        Description = GetRegistryDescription(path),
                        SubKeyCount = key.SubKeyCount,
                        ValueCount = key.ValueCount
                    };
                }
            }
            catch
            {
                // 忽略访问被拒绝的注册表项
            }
            return null;
        }

        private string GetRegistryDescription(string path)
        {
            if (path.Contains("Uninstall")) return "Cursor卸载信息";
            if (path.Contains("ApplicationAssociationToasts")) return "文件关联提示";
            return "Cursor注册表配置";
        }

        private async Task BackupDirectory(ZipArchive archive, string directoryPath)
        {
            await Task.Run(() =>
            {
                try
                {
                    var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories);
                    foreach (var file in files)
                    {
                        try
                        {
                            var entryName = GetRelativeBackupPath(file);
                            archive.CreateEntryFromFile(file, entryName);
                        }
                        catch (Exception ex)
                        {
                            Serilog.Log.Warning(ex, "Failed to backup file in directory: {File}", file);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Serilog.Log.Warning(ex, "Failed to backup directory: {Directory}", directoryPath);
                }
            });
        }

        private async Task BackupRegistry(ZipArchive archive, List<RegistryItem> registryItems)
        {
            await Task.Run(() =>
            {
                try
                {
                    var regContent = new StringBuilder();
                    regContent.AppendLine("Windows Registry Editor Version 5.00");
                    regContent.AppendLine();

                    foreach (var item in registryItems)
                    {
                        try
                        {
                            var regData = ExportRegistryKey(item.Path);
                            if (!string.IsNullOrEmpty(regData))
                            {
                                regContent.AppendLine(regData);
                                regContent.AppendLine();
                            }
                        }
                        catch (Exception ex)
                        {
                            Serilog.Log.Warning(ex, "Failed to export registry: {Path}", item.Path);
                        }
                    }

                    var regEntry = archive.CreateEntry("registry_backup.reg");
                    using var regStream = regEntry.Open();
                    using var writer = new StreamWriter(regStream, Encoding.Unicode);
                    writer.Write(regContent.ToString());
                }
                catch (Exception ex)
                {
                    Serilog.Log.Error(ex, "Failed to backup registry");
                }
            });
        }

        private string ExportRegistryKey(string path)
        {
            // 这里应该实现注册表导出逻辑
            // 由于复杂性，这里只返回一个占位符
            return $"; Registry backup for {path}";
        }

        private void DeleteRegistryPath(string path)
        {
            try
            {
                var parts = path.Split('\\');
                if (parts.Length < 2) return;

                var hive = parts[0] switch
                {
                    "HKEY_CURRENT_USER" => Registry.CurrentUser,
                    "HKEY_LOCAL_MACHINE" => Registry.LocalMachine,
                    "HKEY_CLASSES_ROOT" => Registry.ClassesRoot,
                    _ => null
                };

                if (hive == null) return;

                var keyPath = string.Join("\\", parts.Skip(1));
                hive.DeleteSubKeyTree(keyPath, false);
            }
            catch (Exception ex)
            {
                Serilog.Log.Warning(ex, "Failed to delete registry path: {Path}", path);
            }
        }

        private async Task CleanTempFiles()
        {
            await Task.Run(() =>
            {
                try
                {
                    var tempPaths = new[]
                    {
                        Path.Combine(Path.GetTempPath(), "cursor*"),
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Temp", "cursor*")
                    };

                    foreach (var pattern in tempPaths)
                    {
                        try
                        {
                            var directory = Path.GetDirectoryName(pattern);
                            var searchPattern = Path.GetFileName(pattern);
                            
                            if (Directory.Exists(directory))
                            {
                                var files = Directory.GetFiles(directory, searchPattern);
                                foreach (var file in files)
                                {
                                    try
                                    {
                                        File.Delete(file);
                                    }
                                    catch
                                    {
                                        // 忽略无法删除的文件
                                    }
                                }

                                var directories = Directory.GetDirectories(directory, searchPattern);
                                foreach (var dir in directories)
                                {
                                    try
                                    {
                                        Directory.Delete(dir, true);
                                    }
                                    catch
                                    {
                                        // 忽略无法删除的目录
                                    }
                                }
                            }
                        }
                        catch
                        {
                            // 忽略错误
                        }
                    }
                }
                catch (Exception ex)
                {
                    Serilog.Log.Warning(ex, "Failed to clean temp files");
                }
            });
        }

        private async Task CleanRecentFiles()
        {
            await Task.Run(() =>
            {
                try
                {
                    // 清理最近文件记录
                    var recentPath = Environment.GetFolderPath(Environment.SpecialFolder.Recent);
                    var cursorLinks = Directory.GetFiles(recentPath, "*cursor*", SearchOption.TopDirectoryOnly);
                    
                    foreach (var link in cursorLinks)
                    {
                        try
                        {
                            File.Delete(link);
                        }
                        catch
                        {
                            // 忽略无法删除的文件
                        }
                    }
                }
                catch (Exception ex)
                {
                    Serilog.Log.Warning(ex, "Failed to clean recent files");
                }
            });
        }

        private string GetRelativeBackupPath(string absolutePath)
        {
            // 将绝对路径转换为备份中的相对路径
            var relativePath = absolutePath.Replace(":", "").Replace("\\", "/");
            if (relativePath.StartsWith("/"))
                relativePath = relativePath.Substring(1);
            return relativePath;
        }

        private string GetAbsoluteRestorePath(string relativePath)
        {
            // 将备份中的相对路径转换为绝对路径
            var absolutePath = relativePath.Replace("/", "\\");
            if (absolutePath.Length > 1 && char.IsLetter(absolutePath[0]))
            {
                absolutePath = absolutePath[0] + ":" + absolutePath.Substring(1);
            }
            return absolutePath;
        }

        private async Task RestoreRegistry(ZipArchive archive)
        {
            await Task.Run(() =>
            {
                try
                {
                    var regEntry = archive.GetEntry("registry_backup.reg");
                    if (regEntry != null)
                    {
                        var tempRegFile = Path.GetTempFileName() + ".reg";
                        regEntry.ExtractToFile(tempRegFile, true);

                        // 导入注册表文件
                        var process = Process.Start(new ProcessStartInfo
                        {
                            FileName = "regedit.exe",
                            Arguments = $"/s \"{tempRegFile}\"",
                            UseShellExecute = false,
                            CreateNoWindow = true
                        });

                        process?.WaitForExit();
                        
                        // 清理临时文件
                        try
                        {
                            File.Delete(tempRegFile);
                        }
                        catch
                        {
                            // 忽略清理错误
                        }
                    }
                }
                catch (Exception ex)
                {
                    Serilog.Log.Warning(ex, "Failed to restore registry");
                }
            });
        }

        private string GetBackupDescription(string backupPath)
        {
            try
            {
                using var archive = ZipFile.OpenRead(backupPath);
                var infoEntry = archive.GetEntry("backup_info.json");
                if (infoEntry != null)
                {
                    using var stream = infoEntry.Open();
                    using var reader = new StreamReader(stream);
                    var json = reader.ReadToEnd();
                    var info = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(json);
                    
                    var totalFiles = info?.TotalFiles ?? 0;
                    var totalRegistryItems = info?.TotalRegistryItems ?? 0;
                    var totalSize = info?.TotalSize ?? 0;
                    
                    return $"文件: {totalFiles}, 注册表: {totalRegistryItems}, 大小: {FormatFileSize(totalSize)}";
                }
            }
            catch
            {
                // 忽略错误
            }
            return "备份文件";
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
