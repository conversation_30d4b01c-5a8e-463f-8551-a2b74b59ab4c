using CursorResetTool.Commercial.Models;

namespace CursorResetTool.Commercial.Services
{
    /// <summary>
    /// 用户服务接口
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// 用户注册
        /// </summary>
        Task<(bool success, string message)> RegisterAsync(string username, string email, string password);

        /// <summary>
        /// 用户登录
        /// </summary>
        Task<(bool success, User? user, string message)> LoginAsync(string usernameOrEmail, string password);

        /// <summary>
        /// 获取当前用户
        /// </summary>
        User? GetCurrentUser();

        /// <summary>
        /// 设置当前用户
        /// </summary>
        void SetCurrentUser(User user);

        /// <summary>
        /// 用户登出
        /// </summary>
        void Logout();

        /// <summary>
        /// 更新用户信息
        /// </summary>
        Task<(bool success, string message)> UpdateUserAsync(User user);

        /// <summary>
        /// 修改密码
        /// </summary>
        Task<(bool success, string message)> ChangePasswordAsync(string oldPassword, string newPassword);

        /// <summary>
        /// 重置密码
        /// </summary>
        Task<(bool success, string message)> ResetPasswordAsync(string email);

        /// <summary>
        /// 验证重置密码令牌
        /// </summary>
        Task<(bool success, string message)> ValidateResetTokenAsync(string email, string token, string newPassword);

        /// <summary>
        /// 激活用户账户
        /// </summary>
        Task<(bool success, string message)> ActivateAccountAsync(string email, string activationCode);

        /// <summary>
        /// 检查用户名是否存在
        /// </summary>
        Task<bool> IsUsernameExistsAsync(string username);

        /// <summary>
        /// 检查邮箱是否存在
        /// </summary>
        Task<bool> IsEmailExistsAsync(string email);

        /// <summary>
        /// 保存用户设置
        /// </summary>
        Task<bool> SaveUserSettingsAsync(UserSettings settings);

        /// <summary>
        /// 获取用户设置
        /// </summary>
        UserSettings GetUserSettings();
    }

    /// <summary>
    /// 用户服务实现
    /// </summary>
    public class UserService : IUserService
    {
        private readonly ISecurityService _securityService;
        private readonly INetworkService _networkService;
        private User? _currentUser;
        private readonly string _userDataPath;

        public UserService(ISecurityService securityService, INetworkService networkService)
        {
            _securityService = securityService;
            _networkService = networkService;
            _userDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "尚书CAR", "UserData");
            Directory.CreateDirectory(_userDataPath);
        }

        public async Task<(bool success, string message)> RegisterAsync(string username, string email, string password)
        {
            try
            {
                // 验证输入
                if (string.IsNullOrWhiteSpace(username) || username.Length < 3)
                    return (false, "用户名长度至少3个字符");

                if (string.IsNullOrWhiteSpace(email) || !IsValidEmail(email))
                    return (false, "请输入有效的邮箱地址");

                if (string.IsNullOrWhiteSpace(password) || password.Length < 6)
                    return (false, "密码长度至少6个字符");

                // 检查用户名和邮箱是否已存在
                if (await IsUsernameExistsAsync(username))
                    return (false, "用户名已存在");

                if (await IsEmailExistsAsync(email))
                    return (false, "邮箱已被注册");

                // 创建用户
                var user = new User
                {
                    UserId = Guid.NewGuid().ToString(),
                    Username = username,
                    Email = email,
                    PasswordHash = _securityService.HashPassword(password, out var salt),
                    Salt = salt,
                    MembershipLevel = MembershipLevel.Trial,
                    RegisterTime = DateTime.Now,
                    IsActive = true,
                    ActivationCode = _securityService.GenerateRandomString(32)
                };

                // 网络注册
                var networkResult = await _networkService.RegisterUserAsync(user);
                if (!networkResult.success)
                {
                    // 如果网络注册失败，保存到本地
                    await SaveUserLocallyAsync(user);
                    return (true, "注册成功！由于网络问题，账户信息已保存到本地。");
                }

                // 保存到本地
                await SaveUserLocallyAsync(user);

                return (true, "注册成功！请查收邮件激活账户。");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "User registration failed");
                return (false, "注册失败，请稍后重试");
            }
        }

        public async Task<(bool success, User? user, string message)> LoginAsync(string usernameOrEmail, string password)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(usernameOrEmail) || string.IsNullOrWhiteSpace(password))
                    return (false, null, "请输入用户名/邮箱和密码");

                // 先尝试网络登录
                var networkResult = await _networkService.LoginUserAsync(usernameOrEmail, password);
                if (networkResult.success && networkResult.user != null)
                {
                    _currentUser = networkResult.user;
                    _currentUser.LastLoginTime = DateTime.Now;
                    await SaveUserLocallyAsync(_currentUser);
                    return (true, _currentUser, "登录成功");
                }

                // 网络登录失败，尝试本地登录
                var localUser = await LoadUserLocallyAsync(usernameOrEmail);
                if (localUser != null && _securityService.VerifyPassword(password, localUser.PasswordHash, localUser.Salt))
                {
                    _currentUser = localUser;
                    _currentUser.LastLoginTime = DateTime.Now;
                    await SaveUserLocallyAsync(_currentUser);
                    return (true, _currentUser, "登录成功（离线模式）");
                }

                return (false, null, "用户名/邮箱或密码错误");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "User login failed");
                return (false, null, "登录失败，请稍后重试");
            }
        }

        public User? GetCurrentUser() => _currentUser;

        public void SetCurrentUser(User user) => _currentUser = user;

        public void Logout()
        {
            _currentUser = null;
        }

        public async Task<(bool success, string message)> UpdateUserAsync(User user)
        {
            try
            {
                // 网络更新
                var networkResult = await _networkService.UpdateUserAsync(user);
                
                // 本地更新
                await SaveUserLocallyAsync(user);
                
                if (_currentUser?.UserId == user.UserId)
                {
                    _currentUser = user;
                }

                return (true, "用户信息更新成功");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "User update failed");
                return (false, "更新失败，请稍后重试");
            }
        }

        public async Task<(bool success, string message)> ChangePasswordAsync(string oldPassword, string newPassword)
        {
            try
            {
                if (_currentUser == null)
                    return (false, "请先登录");

                if (!_securityService.VerifyPassword(oldPassword, _currentUser.PasswordHash, _currentUser.Salt))
                    return (false, "原密码错误");

                if (string.IsNullOrWhiteSpace(newPassword) || newPassword.Length < 6)
                    return (false, "新密码长度至少6个字符");

                _currentUser.PasswordHash = _securityService.HashPassword(newPassword, out var salt);
                _currentUser.Salt = salt;

                var result = await UpdateUserAsync(_currentUser);
                return result;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Password change failed");
                return (false, "密码修改失败，请稍后重试");
            }
        }

        public async Task<(bool success, string message)> ResetPasswordAsync(string email)
        {
            try
            {
                var result = await _networkService.ResetPasswordAsync(email);
                return result;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Password reset failed");
                return (false, "密码重置失败，请稍后重试");
            }
        }

        public async Task<(bool success, string message)> ValidateResetTokenAsync(string email, string token, string newPassword)
        {
            try
            {
                var result = await _networkService.ValidateResetTokenAsync(email, token, newPassword);
                return result;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Reset token validation failed");
                return (false, "令牌验证失败，请稍后重试");
            }
        }

        public async Task<(bool success, string message)> ActivateAccountAsync(string email, string activationCode)
        {
            try
            {
                var result = await _networkService.ActivateAccountAsync(email, activationCode);
                return result;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Account activation failed");
                return (false, "账户激活失败，请稍后重试");
            }
        }

        public async Task<bool> IsUsernameExistsAsync(string username)
        {
            try
            {
                // 先检查网络
                var networkExists = await _networkService.CheckUsernameExistsAsync(username);
                if (networkExists) return true;

                // 检查本地
                return await CheckUsernameExistsLocallyAsync(username);
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> IsEmailExistsAsync(string email)
        {
            try
            {
                // 先检查网络
                var networkExists = await _networkService.CheckEmailExistsAsync(email);
                if (networkExists) return true;

                // 检查本地
                return await CheckEmailExistsLocallyAsync(email);
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> SaveUserSettingsAsync(UserSettings settings)
        {
            try
            {
                if (_currentUser != null)
                {
                    _currentUser.Settings = settings;
                    await SaveUserLocallyAsync(_currentUser);
                }

                var settingsPath = Path.Combine(_userDataPath, "settings.json");
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(settings, Newtonsoft.Json.Formatting.Indented);
                await File.WriteAllTextAsync(settingsPath, json);
                return true;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to save user settings");
                return false;
            }
        }

        public UserSettings GetUserSettings()
        {
            try
            {
                if (_currentUser?.Settings != null)
                    return _currentUser.Settings;

                var settingsPath = Path.Combine(_userDataPath, "settings.json");
                if (File.Exists(settingsPath))
                {
                    var json = File.ReadAllText(settingsPath);
                    return Newtonsoft.Json.JsonConvert.DeserializeObject<UserSettings>(json) ?? new UserSettings();
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to load user settings");
            }
            return new UserSettings();
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private async Task SaveUserLocallyAsync(User user)
        {
            try
            {
                var userPath = Path.Combine(_userDataPath, $"{user.UserId}.json");
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(user, Newtonsoft.Json.Formatting.Indented);
                var encryptedJson = _securityService.EncryptAES(json, "UserData2024");
                await File.WriteAllTextAsync(userPath, encryptedJson);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to save user locally");
            }
        }

        private async Task<User?> LoadUserLocallyAsync(string usernameOrEmail)
        {
            try
            {
                var userFiles = Directory.GetFiles(_userDataPath, "*.json");
                foreach (var file in userFiles)
                {
                    if (Path.GetFileName(file) == "settings.json") continue;

                    var encryptedJson = await File.ReadAllTextAsync(file);
                    var json = _securityService.DecryptAES(encryptedJson, "UserData2024");
                    if (string.IsNullOrEmpty(json)) continue;

                    var user = Newtonsoft.Json.JsonConvert.DeserializeObject<User>(json);
                    if (user != null && (user.Username == usernameOrEmail || user.Email == usernameOrEmail))
                    {
                        return user;
                    }
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to load user locally");
            }
            return null;
        }

        private async Task<bool> CheckUsernameExistsLocallyAsync(string username)
        {
            var user = await LoadUserLocallyAsync(username);
            return user != null;
        }

        private async Task<bool> CheckEmailExistsLocallyAsync(string email)
        {
            var user = await LoadUserLocallyAsync(email);
            return user != null;
        }
    }
}
