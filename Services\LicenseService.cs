using System;
using System.IO;
using System.Text.Json;
using Microsoft.Win32;
using CursorReset.Models;

namespace CursorReset.Services
{
    public class LicenseService
    {
        private static readonly string LicenseRegistryPath = @"SOFTWARE\CursorResetTool";
        private static readonly string LicenseFileName = "license.dat";
        private static readonly string TrialFileName = "trial.dat";
        private static readonly string EncryptionKey = "CursorResetTool2024SecretKey";

        public static bool IsLicenseValid()
        {
            try
            {
                var license = LoadLicense();
                if (license == null) return false;

                // 检查许可证是否过期
                if (DateTime.Now > license.ExpiryDate) return false;

                // 检查机器ID是否匹配
                var currentMachineId = CryptographyService.GenerateMachineId();
                if (license.MachineId != currentMachineId) return false;

                // 验证签名
                var dataToVerify = $"{license.LicenseKey}{license.UserEmail}{license.ExpiryDate:yyyy-MM-dd}{license.MachineId}";
                if (!CryptographyService.VerifySignature(dataToVerify, license.Signature, "PublicKey"))
                {
                    return false;
                }

                return license.IsActivated;
            }
            catch
            {
                return false;
            }
        }

        public static TrialInfo GetTrialInfo()
        {
            try
            {
                var trialPath = GetTrialFilePath();
                if (!File.Exists(trialPath))
                {
                    // 首次运行，创建试用信息
                    var newTrial = new TrialInfo
                    {
                        FirstRun = DateTime.Now,
                        LastRun = DateTime.Now,
                        RunCount = 1,
                        TrialDays = 7
                    };
                    SaveTrialInfo(newTrial);
                    return newTrial;
                }

                var encryptedData = File.ReadAllText(trialPath);
                var decryptedData = CryptographyService.DecryptString(encryptedData, EncryptionKey);
                var trial = JsonSerializer.Deserialize<TrialInfo>(decryptedData);
                
                if (trial != null)
                {
                    trial.LastRun = DateTime.Now;
                    trial.RunCount++;
                    SaveTrialInfo(trial);
                }

                return trial ?? new TrialInfo();
            }
            catch
            {
                return new TrialInfo { FirstRun = DateTime.Now, LastRun = DateTime.Now, RunCount = 1 };
            }
        }

        public static void SaveTrialInfo(TrialInfo trial)
        {
            try
            {
                var trialPath = GetTrialFilePath();
                var jsonData = JsonSerializer.Serialize(trial);
                var encryptedData = CryptographyService.EncryptString(jsonData, EncryptionKey);
                
                Directory.CreateDirectory(Path.GetDirectoryName(trialPath)!);
                File.WriteAllText(trialPath, encryptedData);
                
                // 设置文件为隐藏
                File.SetAttributes(trialPath, FileAttributes.Hidden | FileAttributes.System);
            }
            catch
            {
                // 忽略保存错误
            }
        }

        public static LicenseInfo? LoadLicense()
        {
            try
            {
                // 首先尝试从注册表加载
                var license = LoadLicenseFromRegistry();
                if (license != null) return license;

                // 然后尝试从文件加载
                return LoadLicenseFromFile();
            }
            catch
            {
                return null;
            }
        }

        private static LicenseInfo? LoadLicenseFromRegistry()
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(LicenseRegistryPath);
                if (key == null) return null;

                var encryptedData = key.GetValue("LicenseData") as string;
                if (string.IsNullOrEmpty(encryptedData)) return null;

                var decryptedData = CryptographyService.DecryptString(encryptedData, EncryptionKey);
                return JsonSerializer.Deserialize<LicenseInfo>(decryptedData);
            }
            catch
            {
                return null;
            }
        }

        private static LicenseInfo? LoadLicenseFromFile()
        {
            try
            {
                var licensePath = GetLicenseFilePath();
                if (!File.Exists(licensePath)) return null;

                var encryptedData = File.ReadAllText(licensePath);
                var decryptedData = CryptographyService.DecryptString(encryptedData, EncryptionKey);
                return JsonSerializer.Deserialize<LicenseInfo>(decryptedData);
            }
            catch
            {
                return null;
            }
        }

        public static bool SaveLicense(LicenseInfo license)
        {
            try
            {
                var jsonData = JsonSerializer.Serialize(license);
                var encryptedData = CryptographyService.EncryptString(jsonData, EncryptionKey);

                // 保存到注册表
                SaveLicenseToRegistry(encryptedData);

                // 保存到文件
                SaveLicenseToFile(encryptedData);

                return true;
            }
            catch
            {
                return false;
            }
        }

        private static void SaveLicenseToRegistry(string encryptedData)
        {
            try
            {
                using var key = Registry.CurrentUser.CreateSubKey(LicenseRegistryPath);
                key.SetValue("LicenseData", encryptedData);
                key.SetValue("InstallDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            catch
            {
                // 忽略注册表保存错误
            }
        }

        private static void SaveLicenseToFile(string encryptedData)
        {
            try
            {
                var licensePath = GetLicenseFilePath();
                Directory.CreateDirectory(Path.GetDirectoryName(licensePath)!);
                File.WriteAllText(licensePath, encryptedData);
                
                // 设置文件为隐藏
                File.SetAttributes(licensePath, FileAttributes.Hidden | FileAttributes.System);
            }
            catch
            {
                // 忽略文件保存错误
            }
        }

        private static string GetLicenseFilePath()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            return Path.Combine(appDataPath, "CursorResetTool", LicenseFileName);
        }

        private static string GetTrialFilePath()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            return Path.Combine(appDataPath, "CursorResetTool", TrialFileName);
        }

        public static void RemoveLicense()
        {
            try
            {
                // 删除注册表项
                Registry.CurrentUser.DeleteSubKeyTree(LicenseRegistryPath, false);

                // 删除文件
                var licensePath = GetLicenseFilePath();
                if (File.Exists(licensePath))
                {
                    File.SetAttributes(licensePath, FileAttributes.Normal);
                    File.Delete(licensePath);
                }
            }
            catch
            {
                // 忽略删除错误
            }
        }

        public static string GetMachineId()
        {
            return CryptographyService.GenerateMachineId();
        }

        public static bool IsTrialExpired()
        {
            var trial = GetTrialInfo();
            return trial.IsExpired;
        }

        public static int GetRemainingTrialDays()
        {
            var trial = GetTrialInfo();
            return trial.RemainingDays;
        }
    }
}
