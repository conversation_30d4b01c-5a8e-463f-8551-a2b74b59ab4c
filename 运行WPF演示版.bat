@echo off
title WPF Demo Launcher

echo ========================================
echo    Cursor Reset Tool - WPF Demo
echo ========================================
echo.

echo Starting WPF application...
echo.

REM Check if program file exists
if not exist "CursorResetTool-Demo\bin\Debug\net8.0-windows\CursorResetTool-Demo.exe" (
    echo Error: Program file not found!
    echo Please build the project first: dotnet build CursorResetTool-Demo
    echo.
    pause
    exit /b 1
)

REM Start WPF program
echo Launching...
start "" "CursorResetTool-Demo\bin\Debug\net8.0-windows\CursorResetTool-Demo.exe"

echo.
echo Program launched!
echo.
echo If the program doesn't show, please check:
echo 1. .NET 8.0 Runtime is installed
echo 2. Sufficient permissions
echo 3. Check Task Manager for running process
echo.

timeout /t 3 /nobreak >nul
exit
