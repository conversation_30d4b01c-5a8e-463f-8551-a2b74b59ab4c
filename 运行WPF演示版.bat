@echo off
chcp 65001 >nul
title 尚书CAR - WPF演示版启动器

echo ========================================
echo    尚书_CAR_cursor重置系统 - WPF演示版
echo ========================================
echo.

echo 正在启动图形界面程序...
echo.

REM 检查程序文件是否存在
if not exist "CursorResetTool-Demo\bin\Debug\net8.0-windows\CursorResetTool-Demo.exe" (
    echo 错误：找不到程序文件！
    echo 请先编译项目：dotnet build CursorResetTool-Demo
    echo.
    pause
    exit /b 1
)

REM 启动WPF程序
echo 启动中...
start "" "CursorResetTool-Demo\bin\Debug\net8.0-windows\CursorResetTool-Demo.exe"

echo.
echo 程序已启动！
echo.
echo 如果程序没有显示，请检查：
echo 1. 是否安装了 .NET 8.0 Runtime
echo 2. 是否有足够的权限
echo 3. 查看任务管理器中是否有进程运行
echo.

timeout /t 3 /nobreak >nul
exit
