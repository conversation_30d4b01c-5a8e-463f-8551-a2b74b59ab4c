Cursor Reset Tool 使用说明
================================

⚠️ 重要提醒：此工具仅用于学习目的！

📋 使用步骤：

1. 安装依赖
   - 双击运行 setup.bat
   - 如果没有 .NET 8.0 SDK，会提示下载安装
   - 安装完成后重新运行 setup.bat

2. 运行程序
   - 双击运行 run.bat
   - 程序会自动请求管理员权限
   - 按照菜单提示操作

3. 功能说明
   [1] 扫描 - 查找 Cursor 相关文件和注册表项
   [2] 备份 - 将找到的数据备份到桌面
   [3] 清理 - 删除 Cursor 相关数据（谨慎操作！）
   [4] 恢复 - 从备份恢复数据
   [5] 退出 - 退出程序

🛡️ 安全提醒：

- 程序需要管理员权限才能操作系统文件
- 清理前务必先创建备份
- 程序会多次确认危险操作
- 建议在虚拟机中测试

📁 备份位置：

备份文件会保存到桌面的 CursorBackup 文件夹中
文件名格式：CursorBackup_YYYYMMDD_HHMMSS.zip

🔧 技术特点：

- 自动检测和关闭相关进程
- 递归删除文件和目录
- 导出和删除注册表项
- 清理临时文件和缓存
- 完善的错误处理机制

📚 学习价值：

这个项目展示了以下 C# 编程技术：
- 文件系统操作
- 注册表操作
- 进程管理
- 数据压缩
- 异常处理
- 用户界面设计

⚖️ 法律声明：

- 此工具仅用于教育和学习目的
- 使用者需要了解相关风险
- 请遵守软件使用条款
- 作者不承担任何使用责任

如有问题，请查看 README.md 获取更多技术细节。
