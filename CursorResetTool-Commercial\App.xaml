<Application x:Class="CursorResetTool.Commercial.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:ui="http://schemas.modernwpf.com/2019"
             StartupUri="Views/SplashWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Cyan" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Modern WPF -->
                <ui:ThemeResources />
                <ui:XamlControlsResources />
                
                <!-- Custom Styles -->
                <ResourceDictionary Source="Styles/CustomStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Global Colors -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#03DAC6"/>
            <SolidColorBrush x:Key="AccentBrush" Color="#FF4081"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
            
            <!-- Global Fonts -->
            <FontFamily x:Key="PrimaryFont">Segoe UI, Microsoft YaHei UI, SimSun</FontFamily>
            <FontFamily x:Key="MonospaceFont">Consolas, Courier New</FontFamily>
        </ResourceDictionary>
    </Application.Resources>
</Application>
