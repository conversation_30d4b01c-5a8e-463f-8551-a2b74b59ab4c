<Application x:Class="CursorResetTool.Commercial.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Cyan" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />

                <!-- Modern WPF -->
                <ui:ThemeResources />
                <ui:XamlControlsResources />

                <!-- Custom Styles -->
                <ResourceDictionary Source="Styles/CustomStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Colors -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#1976D2"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#03DAC6"/>
            <SolidColorBrush x:Key="AccentBrush" Color="#FF4081"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="TextPrimaryBrush" Color="#212121"/>
            <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>

            <!-- Global Fonts -->
            <FontFamily x:Key="PrimaryFont">Microsoft YaHei UI, Segoe UI, SimSun</FontFamily>
            <FontFamily x:Key="MonospaceFont">Consolas, Courier New</FontFamily>

            <!-- Global Styles -->
            <Style x:Key="TitleTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
                <Setter Property="FontSize" Value="24"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            </Style>

            <Style x:Key="SubtitleTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
            </Style>

            <Style x:Key="BodyTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
