<Window x:Class="CursorResetTool.Demo.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="尚书_CAR_cursor重置系统 - 演示版"
        Width="1000" Height="700"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部工具栏 -->
        <Border Grid.Row="0" 
               Background="{StaticResource PrimaryBrush}"
               Height="60">
            <Grid>
                <StackPanel Orientation="Horizontal" 
                           VerticalAlignment="Center"
                           Margin="20,0">
                    <materialDesign:PackIcon Kind="CursorDefault"
                                           Width="32" Height="32"
                                           Foreground="White"
                                           VerticalAlignment="Center"/>
                    
                    <TextBlock Text="尚书_CAR_cursor重置系统"
                              Style="{StaticResource TitleTextStyle}"
                              FontSize="20"
                              Foreground="White"
                              VerticalAlignment="Center"
                              Margin="15,0,0,0"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           Margin="20,0">
                    
                    <TextBlock Text="演示版本 | 版本: 2.0.0"
                              Style="{StaticResource BodyTextStyle}"
                              Foreground="White"
                              VerticalAlignment="Center"
                              Margin="0,0,20,0"/>

                    <Button Content="关于"
                           Background="Transparent"
                           Foreground="White"
                           BorderThickness="1"
                           BorderBrush="White"
                           Width="60" Height="30"
                           Click="AboutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧功能菜单 -->
            <Border Grid.Column="0" 
                   Style="{StaticResource CardStyle}"
                   Margin="0,0,10,0">
                <StackPanel>
                    
                    <TextBlock Text="主要功能"
                              Style="{StaticResource TitleTextStyle}"
                              FontSize="18"
                              Margin="0,0,0,20"/>

                    <!-- 扫描功能 -->
                    <Button x:Name="ScanButton"
                           Style="{StaticResource PrimaryButtonStyle}"
                           Height="50"
                           Margin="0,0,0,10"
                           Click="ScanButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Magnify" Width="20" Height="20" Margin="0,0,10,0"/>
                            <TextBlock Text="扫描 Cursor 数据" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- 备份功能 -->
                    <Button x:Name="BackupButton"
                           Style="{StaticResource SecondaryButtonStyle}"
                           Height="50"
                           Margin="0,0,0,10"
                           Click="BackupButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Backup" Width="20" Height="20" Margin="0,0,10,0"/>
                            <TextBlock Text="备份 Cursor 数据" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- 清理功能 -->
                    <Button x:Name="CleanButton"
                           Style="{StaticResource DangerButtonStyle}"
                           Height="50"
                           Margin="0,0,0,10"
                           Click="CleanButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Delete" Width="20" Height="20" Margin="0,0,10,0"/>
                            <TextBlock Text="清理 Cursor 数据" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- 恢复功能 -->
                    <Button x:Name="RestoreButton"
                           Style="{StaticResource SuccessButtonStyle}"
                           Height="50"
                           Margin="0,0,0,20"
                           Click="RestoreButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Restore" Width="20" Height="20" Margin="0,0,10,0"/>
                            <TextBlock Text="恢复 Cursor 数据" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- 许可证信息 -->
                    <Border Background="{StaticResource BackgroundBrush}"
                           CornerRadius="8"
                           Padding="15"
                           Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock Text="许可证信息"
                                      Style="{StaticResource SubtitleTextStyle}"
                                      FontSize="14"
                                      Margin="0,0,0,10"/>
                            
                            <TextBlock Text="类型: 演示版"
                                      Style="{StaticResource BodyTextStyle}"
                                      FontSize="12"
                                      Margin="0,0,0,5"/>
                            
                            <TextBlock Text="状态: 完全功能"
                                      Style="{StaticResource BodyTextStyle}"
                                      FontSize="12"/>
                        </StackPanel>
                    </Border>

                    <!-- 系统信息 -->
                    <Border Background="{StaticResource BackgroundBrush}"
                           CornerRadius="8"
                           Padding="15">
                        <StackPanel>
                            <TextBlock Text="系统信息"
                                      Style="{StaticResource SubtitleTextStyle}"
                                      FontSize="14"
                                      Margin="0,0,0,10"/>
                            
                            <TextBlock x:Name="OSText"
                                      Text="操作系统: Windows 11"
                                      Style="{StaticResource BodyTextStyle}"
                                      FontSize="12"
                                      Margin="0,0,0,5"/>
                            
                            <TextBlock x:Name="ArchText"
                                      Text="架构: x64"
                                      Style="{StaticResource BodyTextStyle}"
                                      FontSize="12"/>
                        </StackPanel>
                    </Border>

                </StackPanel>
            </Border>

            <!-- 右侧内容区域 -->
            <Border Grid.Column="1" 
                   Style="{StaticResource CardStyle}"
                   Margin="10,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 操作标题 -->
                    <TextBlock x:Name="OperationTitle"
                              Grid.Row="0"
                              Text="欢迎使用 Cursor 重置工具"
                              Style="{StaticResource TitleTextStyle}"
                              FontSize="20"
                              Margin="0,0,0,20"/>

                    <!-- 内容区域 -->
                    <ScrollViewer Grid.Row="1" 
                                 VerticalScrollBarVisibility="Auto">
                        <StackPanel x:Name="ContentPanel">
                            
                            <!-- 欢迎信息 -->
                            <Border Background="{StaticResource BackgroundBrush}"
                                   CornerRadius="8"
                                   Padding="20"
                                   Margin="0,0,0,20">
                                <StackPanel>
                                    <TextBlock Text="🎉 欢迎使用专业的 Cursor 重置工具！"
                                              Style="{StaticResource SubtitleTextStyle}"
                                              FontSize="16"
                                              Margin="0,0,0,15"/>
                                    
                                    <TextBlock Text="功能特点："
                                              Style="{StaticResource BodyTextStyle}"
                                              FontWeight="Medium"
                                              Margin="0,0,0,10"/>
                                    
                                    <TextBlock Text="• 智能扫描 - 自动识别所有 Cursor 相关文件和注册表项"
                                              Style="{StaticResource BodyTextStyle}"
                                              Margin="0,0,0,5"/>
                                    
                                    <TextBlock Text="• 安全备份 - 完整备份所有数据，支持一键恢复"
                                              Style="{StaticResource BodyTextStyle}"
                                              Margin="0,0,0,5"/>
                                    
                                    <TextBlock Text="• 彻底清理 - 多层清理机制，确保完全重置"
                                              Style="{StaticResource BodyTextStyle}"
                                              Margin="0,0,0,5"/>
                                    
                                    <TextBlock Text="• 企业级安全 - 一机一码授权，防止盗版传播"
                                              Style="{StaticResource BodyTextStyle}"
                                              Margin="0,0,0,15"/>
                                    
                                    <TextBlock Text="使用建议：扫描 → 备份 → 清理 → 重启电脑"
                                              Style="{StaticResource BodyTextStyle}"
                                              FontWeight="Medium"
                                              Foreground="{StaticResource PrimaryBrush}"/>
                                </StackPanel>
                            </Border>

                            <!-- 技术特性 -->
                            <Border Background="{StaticResource BackgroundBrush}"
                                   CornerRadius="8"
                                   Padding="20">
                                <StackPanel>
                                    <TextBlock Text="技术特性"
                                              Style="{StaticResource SubtitleTextStyle}"
                                              FontSize="16"
                                              Margin="0,0,0,15"/>
                                    
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                            <TextBlock Text="🎨 现代化UI设计" Style="{StaticResource BodyTextStyle}" Margin="0,0,0,5"/>
                                            <TextBlock Text="🔐 企业级安全机制" Style="{StaticResource BodyTextStyle}" Margin="0,0,0,5"/>
                                            <TextBlock Text="🛡️ 一机一码防盗版" Style="{StaticResource BodyTextStyle}" Margin="0,0,0,5"/>
                                            <TextBlock Text="🌐 多语言支持" Style="{StaticResource BodyTextStyle}"/>
                                        </StackPanel>
                                        
                                        <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                            <TextBlock Text="🔄 自动更新系统" Style="{StaticResource BodyTextStyle}" Margin="0,0,0,5"/>
                                            <TextBlock Text="📊 完整日志记录" Style="{StaticResource BodyTextStyle}" Margin="0,0,0,5"/>
                                            <TextBlock Text="👥 用户管理系统" Style="{StaticResource BodyTextStyle}" Margin="0,0,0,5"/>
                                            <TextBlock Text="💼 商业级许可证" Style="{StaticResource BodyTextStyle}"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                        </StackPanel>
                    </ScrollViewer>

                    <!-- 进度条 -->
                    <ProgressBar x:Name="OperationProgress"
                                Grid.Row="2"
                                Height="6"
                                Margin="0,20,0,0"
                                Visibility="Collapsed"
                                Background="LightGray"
                                Foreground="{StaticResource PrimaryBrush}"/>

                </Grid>
            </Border>

        </Grid>

        <!-- 底部状态栏 -->
        <Border Grid.Row="2" 
               Background="{StaticResource SurfaceBrush}"
               BorderThickness="0,1,0,0"
               BorderBrush="{StaticResource PrimaryBrush}"
               Height="35">
            <Grid>
                <TextBlock x:Name="StatusText"
                          Text="就绪 - 请选择要执行的操作"
                          Style="{StaticResource BodyTextStyle}"
                          FontSize="12"
                          VerticalAlignment="Center"
                          Margin="20,0"/>

                <TextBlock Text="Copyright © 2024 尚书科技 | 演示版本"
                          Style="{StaticResource BodyTextStyle}"
                          FontSize="10"
                          Foreground="{StaticResource TextSecondaryBrush}"
                          HorizontalAlignment="Right"
                          VerticalAlignment="Center"
                          Margin="20,0"/>
            </Grid>
        </Border>

    </Grid>
</Window>
