using System;
using System.Threading.Tasks;
using CursorReset.Models;
using CursorReset.Services;
using CursorReset.Server;

namespace CursorReset.UI
{
    public class LicenseManager
    {
        public static void ShowLicenseMenu()
        {
            while (true)
            {
                Console.Clear();
                Console.WriteLine("=== Cursor Reset Tool - 许可证管理 ===");
                Console.WriteLine();

                var license = LicenseService.LoadLicense();
                if (license != null && LicenseService.IsLicenseValid())
                {
                    ShowLicenseInfo(license);
                    Console.WriteLine();
                    Console.WriteLine("1. 返回主菜单");
                    Console.WriteLine("2. 重新激活");
                    Console.WriteLine("3. 移除许可证");
                    Console.WriteLine("4. 检查在线状态");
                    Console.Write("请选择 (1-4): ");
                }
                else
                {
                    ShowTrialInfo();
                    Console.WriteLine();
                    Console.WriteLine("1. 激活许可证");
                    Console.WriteLine("2. 离线激活");
                    Console.WriteLine("3. 申请试用");
                    Console.WriteLine("4. 查看测试许可证");
                    Console.WriteLine("5. 返回主菜单");
                    Console.Write("请选择 (1-5): ");
                }

                var choice = Console.ReadLine();
                Console.WriteLine();

                if (license != null && LicenseService.IsLicenseValid())
                {
                    switch (choice)
                    {
                        case "1":
                            return;
                        case "2":
                            ReactivateLicense();
                            break;
                        case "3":
                            RemoveLicense();
                            break;
                        case "4":
                            CheckOnlineStatus();
                            break;
                        default:
                            Console.WriteLine("无效选项");
                            break;
                    }
                }
                else
                {
                    switch (choice)
                    {
                        case "1":
                            ActivateLicense();
                            break;
                        case "2":
                            ActivateLicenseOffline();
                            break;
                        case "3":
                            ApplyTrial();
                            break;
                        case "4":
                            ShowTestLicenses();
                            break;
                        case "5":
                            return;
                        default:
                            Console.WriteLine("无效选项");
                            break;
                    }
                }

                if (choice != "1" && choice != "4" && choice != "5")
                {
                    Console.WriteLine("\n按任意键继续...");
                    Console.ReadKey();
                }
            }
        }

        private static void ShowLicenseInfo(LicenseInfo license)
        {
            Console.WriteLine("✅ 许可证状态: 已激活");
            Console.WriteLine($"📧 用户邮箱: {license.UserEmail}");
            Console.WriteLine($"👤 用户名称: {license.UserName}");
            Console.WriteLine($"🔑 许可证密钥: {license.LicenseKey}");
            Console.WriteLine($"📅 发行日期: {license.IssueDate:yyyy-MM-dd}");
            Console.WriteLine($"⏰ 到期日期: {license.ExpiryDate:yyyy-MM-dd}");
            Console.WriteLine($"🏷️ 许可证类型: {GetLicenseTypeText(license.Type)}");
            Console.WriteLine($"💻 机器ID: {license.MachineId}");
            Console.WriteLine($"🔢 版本: {license.ProductVersion}");
            
            var remainingDays = (license.ExpiryDate - DateTime.Now).Days;
            if (remainingDays > 0)
            {
                Console.WriteLine($"⏳ 剩余天数: {remainingDays} 天");
            }
            else
            {
                Console.WriteLine("⚠️ 许可证已过期");
            }
        }

        private static void ShowTrialInfo()
        {
            var trial = LicenseService.GetTrialInfo();
            
            Console.WriteLine("🔓 许可证状态: 未激活");
            Console.WriteLine($"📅 首次运行: {trial.FirstRun:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"🔢 运行次数: {trial.RunCount}");
            Console.WriteLine($"⏳ 试用期: {trial.TrialDays} 天");
            
            if (trial.IsExpired)
            {
                Console.WriteLine("❌ 试用期已过期");
            }
            else
            {
                Console.WriteLine($"⏰ 剩余天数: {trial.RemainingDays} 天");
            }
            
            Console.WriteLine($"💻 机器ID: {LicenseService.GetMachineId()}");
        }

        private static void ActivateLicense()
        {
            Console.WriteLine("=== 在线激活许可证 ===");
            Console.WriteLine();
            
            Console.Write("请输入许可证密钥: ");
            var licenseKey = Console.ReadLine()?.Trim();
            
            if (string.IsNullOrEmpty(licenseKey))
            {
                Console.WriteLine("❌ 许可证密钥不能为空");
                return;
            }
            
            Console.Write("请输入邮箱地址: ");
            var email = Console.ReadLine()?.Trim();
            
            if (string.IsNullOrEmpty(email))
            {
                Console.WriteLine("❌ 邮箱地址不能为空");
                return;
            }
            
            Console.WriteLine("\n🔄 正在激活许可证...");
            
            Task.Run(async () =>
            {
                var result = await ActivationService.ActivateLicenseAsync(licenseKey, email);
                
                if (result.Success)
                {
                    Console.WriteLine("✅ 许可证激活成功！");
                    if (result.License != null)
                    {
                        Console.WriteLine($"许可证类型: {GetLicenseTypeText(result.License.Type)}");
                        Console.WriteLine($"到期日期: {result.License.ExpiryDate:yyyy-MM-dd}");
                    }
                }
                else
                {
                    Console.WriteLine($"❌ 激活失败: {result.Message}");
                    if (!string.IsNullOrEmpty(result.ErrorCode))
                    {
                        Console.WriteLine($"错误代码: {result.ErrorCode}");
                    }
                }
            }).Wait();
        }

        private static void ActivateLicenseOffline()
        {
            Console.WriteLine("=== 离线激活许可证 ===");
            Console.WriteLine();
            
            Console.Write("请输入许可证密钥: ");
            var licenseKey = Console.ReadLine()?.Trim();
            
            if (string.IsNullOrEmpty(licenseKey))
            {
                Console.WriteLine("❌ 许可证密钥不能为空");
                return;
            }
            
            Console.Write("请输入邮箱地址: ");
            var email = Console.ReadLine()?.Trim();
            
            if (string.IsNullOrEmpty(email))
            {
                Console.WriteLine("❌ 邮箱地址不能为空");
                return;
            }
            
            Console.WriteLine("\n🔄 正在离线激活许可证...");
            
            var result = ActivationService.ActivateLicenseOffline(licenseKey, email);
            
            if (result.Success)
            {
                Console.WriteLine("✅ 离线激活成功！");
                if (result.License != null)
                {
                    Console.WriteLine($"许可证类型: {GetLicenseTypeText(result.License.Type)}");
                    Console.WriteLine($"到期日期: {result.License.ExpiryDate:yyyy-MM-dd}");
                }
            }
            else
            {
                Console.WriteLine($"❌ 离线激活失败: {result.Message}");
            }
        }

        private static void ApplyTrial()
        {
            Console.WriteLine("=== 申请试用许可证 ===");
            Console.WriteLine();
            
            var trial = LicenseService.GetTrialInfo();
            if (trial.IsExpired)
            {
                Console.WriteLine("❌ 试用期已过期，无法申请新的试用许可证");
                return;
            }
            
            Console.WriteLine("🔄 正在生成试用许可证...");
            
            var trialKey = ActivationService.GenerateTrialLicense();
            
            Console.WriteLine("✅ 试用许可证生成成功！");
            Console.WriteLine($"试用密钥: {trialKey}");
            Console.WriteLine($"试用期限: 7 天");
            Console.WriteLine("💡 试用期间可以使用所有功能");
        }

        private static void ReactivateLicense()
        {
            Console.WriteLine("=== 重新激活许可证 ===");
            Console.WriteLine();
            
            var license = LicenseService.LoadLicense();
            if (license == null)
            {
                Console.WriteLine("❌ 没有找到许可证信息");
                return;
            }
            
            Console.WriteLine("🔄 正在重新激活许可证...");
            
            Task.Run(async () =>
            {
                var result = await ActivationService.ActivateLicenseAsync(license.LicenseKey, license.UserEmail);
                
                if (result.Success)
                {
                    Console.WriteLine("✅ 重新激活成功！");
                }
                else
                {
                    Console.WriteLine($"❌ 重新激活失败: {result.Message}");
                }
            }).Wait();
        }

        private static void RemoveLicense()
        {
            Console.WriteLine("=== 移除许可证 ===");
            Console.WriteLine();
            
            Console.Write("确定要移除许可证吗？(输入 'YES' 确认): ");
            var confirmation = Console.ReadLine();
            
            if (confirmation == "YES")
            {
                LicenseService.RemoveLicense();
                Console.WriteLine("✅ 许可证已移除");
            }
            else
            {
                Console.WriteLine("❌ 操作已取消");
            }
        }

        private static void CheckOnlineStatus()
        {
            Console.WriteLine("=== 检查在线状态 ===");
            Console.WriteLine();
            
            Console.WriteLine("🔄 正在验证许可证...");
            
            Task.Run(async () =>
            {
                var isValid = await ActivationService.ValidateLicenseOnlineAsync();
                
                if (isValid)
                {
                    Console.WriteLine("✅ 许可证在线验证成功");
                }
                else
                {
                    Console.WriteLine("❌ 许可证在线验证失败");
                }
            }).Wait();
        }

        private static void ShowTestLicenses()
        {
            Console.WriteLine("=== 测试许可证密钥 ===");
            Console.WriteLine();
            Console.WriteLine("💡 以下是可用于测试的许可证密钥：");
            Console.WriteLine();

            MockLicenseServer.PrintTestLicenses();

            Console.WriteLine("🔧 开发者选项：");
            Console.WriteLine("1. 生成新的个人版许可证");
            Console.WriteLine("2. 生成新的专业版许可证");
            Console.WriteLine("3. 生成新的企业版许可证");
            Console.WriteLine("4. 返回");
            Console.Write("请选择 (1-4): ");

            var choice = Console.ReadLine();
            Console.WriteLine();

            switch (choice)
            {
                case "1":
                    var personalKey = MockLicenseServer.GenerateTestLicense(LicenseType.Personal, 365);
                    Console.WriteLine($"✅ 生成个人版许可证: {personalKey}");
                    break;
                case "2":
                    var proKey = MockLicenseServer.GenerateTestLicense(LicenseType.Professional, 365);
                    Console.WriteLine($"✅ 生成专业版许可证: {proKey}");
                    break;
                case "3":
                    var enterpriseKey = MockLicenseServer.GenerateTestLicense(LicenseType.Enterprise, 1095);
                    Console.WriteLine($"✅ 生成企业版许可证: {enterpriseKey}");
                    break;
                case "4":
                default:
                    return;
            }
        }

        private static string GetLicenseTypeText(LicenseType type)
        {
            return type switch
            {
                LicenseType.Trial => "试用版",
                LicenseType.Personal => "个人版",
                LicenseType.Professional => "专业版",
                LicenseType.Enterprise => "企业版",
                _ => "未知"
            };
        }
    }
}
