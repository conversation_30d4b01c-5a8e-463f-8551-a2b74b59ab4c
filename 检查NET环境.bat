@echo off
title .NET Environment Check

echo ========================================
echo    .NET Environment Check
echo ========================================
echo.

echo Checking .NET installations...
echo.

REM Check if dotnet command is available
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] .NET SDK/Runtime is installed
    echo.
    echo Installed .NET versions:
    dotnet --list-runtimes
    echo.
    echo Installed .NET SDKs:
    dotnet --list-sdks
    echo.
    
    REM Check specifically for .NET 8.0
    dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App 8.0" >nul
    if %errorlevel% equ 0 (
        echo [OK] .NET 8.0 Windows Desktop Runtime is installed
        echo [OK] WPF applications should run without issues
    ) else (
        echo [WARNING] .NET 8.0 Windows Desktop Runtime not found
        echo [INFO] You may need to install .NET 8.0 Desktop Runtime
    )
) else (
    echo [ERROR] .NET is not installed or not in PATH
    echo [INFO] Please install .NET 8.0 Runtime from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
)

echo.
echo ========================================
echo.
pause
