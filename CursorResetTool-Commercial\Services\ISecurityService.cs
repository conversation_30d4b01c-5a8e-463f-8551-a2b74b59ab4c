using System.Security.Principal;

namespace CursorResetTool.Commercial.Services
{
    /// <summary>
    /// 安全服务接口
    /// </summary>
    public interface ISecurityService
    {
        /// <summary>
        /// 检查是否以管理员身份运行
        /// </summary>
        bool IsRunAsAdministrator();

        /// <summary>
        /// 生成密码哈希
        /// </summary>
        string HashPassword(string password, out string salt);

        /// <summary>
        /// 验证密码
        /// </summary>
        bool VerifyPassword(string password, string hash, string salt);

        /// <summary>
        /// 生成随机字符串
        /// </summary>
        string GenerateRandomString(int length);

        /// <summary>
        /// AES加密
        /// </summary>
        string EncryptAES(string plainText, string key);

        /// <summary>
        /// AES解密
        /// </summary>
        string DecryptAES(string cipherText, string key);

        /// <summary>
        /// 生成数字签名
        /// </summary>
        string GenerateDigitalSignature(string data, string privateKey);

        /// <summary>
        /// 验证数字签名
        /// </summary>
        bool VerifyDigitalSignature(string data, string signature, string publicKey);

        /// <summary>
        /// 生成RSA密钥对
        /// </summary>
        (string publicKey, string privateKey) GenerateRSAKeyPair();
    }

    /// <summary>
    /// 安全服务实现
    /// </summary>
    public class SecurityService : ISecurityService
    {
        public bool IsRunAsAdministrator()
        {
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        public string HashPassword(string password, out string salt)
        {
            salt = BCrypt.Net.BCrypt.GenerateSalt();
            return BCrypt.Net.BCrypt.HashPassword(password, salt);
        }

        public bool VerifyPassword(string password, string hash, string salt)
        {
            try
            {
                return BCrypt.Net.BCrypt.Verify(password, hash);
            }
            catch
            {
                return false;
            }
        }

        public string GenerateRandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        public string EncryptAES(string plainText, string key)
        {
            try
            {
                using var aes = System.Security.Cryptography.Aes.Create();
                aes.Key = System.Security.Cryptography.SHA256.HashData(System.Text.Encoding.UTF8.GetBytes(key));
                aes.IV = new byte[16]; // 使用零IV，实际应用中应使用随机IV

                using var encryptor = aes.CreateEncryptor();
                using var msEncrypt = new MemoryStream();
                using var csEncrypt = new System.Security.Cryptography.CryptoStream(msEncrypt, encryptor, System.Security.Cryptography.CryptoStreamMode.Write);
                using var swEncrypt = new StreamWriter(csEncrypt);
                
                swEncrypt.Write(plainText);
                return Convert.ToBase64String(msEncrypt.ToArray());
            }
            catch
            {
                return string.Empty;
            }
        }

        public string DecryptAES(string cipherText, string key)
        {
            try
            {
                using var aes = System.Security.Cryptography.Aes.Create();
                aes.Key = System.Security.Cryptography.SHA256.HashData(System.Text.Encoding.UTF8.GetBytes(key));
                aes.IV = new byte[16]; // 使用零IV，实际应用中应使用随机IV

                using var decryptor = aes.CreateDecryptor();
                using var msDecrypt = new MemoryStream(Convert.FromBase64String(cipherText));
                using var csDecrypt = new System.Security.Cryptography.CryptoStream(msDecrypt, decryptor, System.Security.Cryptography.CryptoStreamMode.Read);
                using var srDecrypt = new StreamReader(csDecrypt);
                
                return srDecrypt.ReadToEnd();
            }
            catch
            {
                return string.Empty;
            }
        }

        public string GenerateDigitalSignature(string data, string privateKey)
        {
            try
            {
                using var rsa = System.Security.Cryptography.RSA.Create();
                rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);
                
                var dataBytes = System.Text.Encoding.UTF8.GetBytes(data);
                var signature = rsa.SignData(dataBytes, System.Security.Cryptography.HashAlgorithmName.SHA256, System.Security.Cryptography.RSASignaturePadding.Pkcs1);
                
                return Convert.ToBase64String(signature);
            }
            catch
            {
                return string.Empty;
            }
        }

        public bool VerifyDigitalSignature(string data, string signature, string publicKey)
        {
            try
            {
                using var rsa = System.Security.Cryptography.RSA.Create();
                rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey), out _);
                
                var dataBytes = System.Text.Encoding.UTF8.GetBytes(data);
                var signatureBytes = Convert.FromBase64String(signature);
                
                return rsa.VerifyData(dataBytes, signatureBytes, System.Security.Cryptography.HashAlgorithmName.SHA256, System.Security.Cryptography.RSASignaturePadding.Pkcs1);
            }
            catch
            {
                return false;
            }
        }

        public (string publicKey, string privateKey) GenerateRSAKeyPair()
        {
            try
            {
                using var rsa = System.Security.Cryptography.RSA.Create(2048);
                
                var publicKey = Convert.ToBase64String(rsa.ExportRSAPublicKey());
                var privateKey = Convert.ToBase64String(rsa.ExportRSAPrivateKey());
                
                return (publicKey, privateKey);
            }
            catch
            {
                return (string.Empty, string.Empty);
            }
        }
    }
}
