@echo off
chcp 65001 >nul
title Cursor Reset Tool Pro - 启动器

echo ========================================
echo    Cursor Reset Tool Pro - 启动器
echo ========================================
echo.

echo 🔍 检查系统环境...

REM 检查是否为64位系统
if not "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    if not "%PROCESSOR_ARCHITEW6432%"=="AMD64" (
        echo ❌ 此版本需要64位系统
        echo 请使用 运行工具-x86.bat
        pause
        exit /b 1
    )
)

echo ✅ 64位系统确认

REM 检查程序文件是否存在
if not exist "CursorResetTool-x64.exe" (
    if not exist "bin\Debug\net8.0\CursorReset.exe" (
        if not exist "bin\Release\net8.0\CursorReset.exe" (
            echo ❌ 找不到程序文件
            echo 请确保以下文件存在：
            echo   - CursorResetTool-x64.exe (发布版)
            echo   或
            echo   - bin\Debug\net8.0\CursorReset.exe (开发版)
            echo   - bin\Release\net8.0\CursorReset.exe (编译版)
            pause
            exit /b 1
        )
    )
)

echo ✅ 程序文件确认

REM 检查管理员权限
echo 🔐 检查管理员权限...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ 需要管理员权限运行
    echo 正在请求管理员权限...
    echo.
    
    REM 尝试以管理员身份重新启动
    if exist "CursorResetTool-x64.exe" (
        powershell -Command "Start-Process 'CursorResetTool-x64.exe' -Verb RunAs"
    ) else if exist "bin\Release\net8.0\CursorReset.exe" (
        powershell -Command "Start-Process 'bin\Release\net8.0\CursorReset.exe' -Verb RunAs"
    ) else if exist "bin\Debug\net8.0\CursorReset.exe" (
        powershell -Command "Start-Process 'bin\Debug\net8.0\CursorReset.exe' -Verb RunAs"
    ) else (
        echo ❌ 无法找到可执行文件
        pause
        exit /b 1
    )
    
    echo ✅ 已请求管理员权限，请在新窗口中操作
    echo 💡 如果没有弹出新窗口，请手动右键程序文件选择"以管理员身份运行"
    pause
    exit /b 0
)

echo ✅ 管理员权限确认
echo.

echo 🚀 启动 Cursor Reset Tool Pro...
echo.
echo 💡 使用提示：
echo   - 首次使用建议选择"申请试用"
echo   - 清理前务必先备份数据
echo   - 遇到问题可查看程序内的日志功能
echo.

REM 启动程序
if exist "CursorResetTool-x64.exe" (
    echo 启动发布版程序...
    "CursorResetTool-x64.exe"
) else if exist "bin\Release\net8.0\CursorReset.exe" (
    echo 启动编译版程序...
    "bin\Release\net8.0\CursorReset.exe"
) else if exist "bin\Debug\net8.0\CursorReset.exe" (
    echo 启动调试版程序...
    "bin\Debug\net8.0\CursorReset.exe"
) else (
    echo ❌ 程序启动失败：找不到可执行文件
    pause
    exit /b 1
)

echo.
echo 📋 程序已退出
echo.
echo 💡 提示：
echo   - 如果程序运行出现问题，请查看日志文件
echo   - 日志位置：%%APPDATA%%\CursorResetTool\logs\
echo   - 如需技术支持，请提供日志文件内容
echo.
pause
