# Cursor Reset Tool

## ⚠️ 重要声明

**此工具仅用于学习和教育目的！**

本项目是一个用于学习 C# 编程的示例程序，展示了以下技术：
- 文件系统操作
- 注册表操作
- 进程管理
- 数据备份和恢复
- 异常处理

## 🎯 功能特性

### 1. 数据扫描
- 扫描 Cursor 相关的文件和目录
- 检查注册表项
- 显示文件大小和统计信息

### 2. 数据备份
- 自动备份找到的文件和目录
- 导出注册表项到 .reg 文件
- 创建压缩备份文件

### 3. 数据清理
- 安全删除文件和目录
- 清理注册表项
- 关闭相关进程
- 清理临时文件和缓存

### 4. 数据恢复
- 列出可用的备份文件
- 提供恢复指导

## 🛠️ 技术实现

### 核心类结构
```
CursorReset/
├── Program.cs              # 主程序入口
├── CursorDataScanner.cs    # 数据扫描器
├── CursorBackup.cs         # 备份管理器
└── CursorDataCleaner.cs    # 数据清理器
```

### 关键技术点

#### 1. 文件系统操作
```csharp
// 递归删除目录
Directory.Delete(path, true);

// 处理只读文件
File.SetAttributes(path, FileAttributes.Normal);
```

#### 2. 注册表操作
```csharp
// 打开注册表项
using var key = Registry.CurrentUser.OpenSubKey(keyPath);

// 删除注册表项
parentKey.DeleteSubKeyTree(keyName, false);
```

#### 3. 进程管理
```csharp
// 优雅关闭进程
process.CloseMainWindow();
if (!process.WaitForExit(5000))
{
    process.Kill();
}
```

#### 4. 数据备份
```csharp
// 创建 ZIP 压缩文件
using var archive = ZipFile.Open(backupPath, ZipArchiveMode.Create);
var entry = archive.CreateEntry(relativePath);
```

## 🚀 使用方法

### 编译和运行

1. **安装 .NET 8.0 SDK**
   ```bash
   # 检查是否已安装
   dotnet --version
   ```

2. **编译项目**
   ```bash
   dotnet build
   ```

3. **以管理员身份运行**
   ```bash
   # 必须以管理员权限运行
   dotnet run
   ```

### 操作流程

1. **扫描数据** - 了解系统中的 Cursor 相关文件
2. **创建备份** - 在清理前备份重要数据
3. **执行清理** - 谨慎执行清理操作
4. **验证结果** - 检查清理效果

## 📋 扫描的位置

### 文件系统位置
- `%APPDATA%\Cursor` - 应用数据
- `%LOCALAPPDATA%\Cursor` - 本地数据
- `%USERPROFILE%\.cursor` - 用户配置
- `%PROGRAMFILES%\Cursor` - 程序文件
- 桌面和开始菜单快捷方式

### 注册表位置
- `HKEY_CURRENT_USER\Software\Cursor`
- `HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Uninstall\Cursor`
- 文件关联和应用程序注册项

## ⚠️ 安全注意事项

1. **管理员权限** - 程序需要管理员权限才能操作系统文件和注册表
2. **数据备份** - 在执行清理前务必创建备份
3. **确认操作** - 程序会要求多次确认危险操作
4. **进程检查** - 自动关闭相关进程以避免文件占用

## 🔧 错误处理

程序包含完善的错误处理机制：
- 权限不足时的提示
- 文件占用时的处理
- 注册表访问异常的处理
- 详细的错误信息输出

## 📚 学习价值

这个项目展示了以下 C# 编程概念：

1. **面向对象设计** - 清晰的类结构和职责分离
2. **异常处理** - 完善的 try-catch 机制
3. **文件 I/O** - 文件和目录操作
4. **系统编程** - 注册表和进程操作
5. **用户交互** - 控制台界面设计
6. **数据压缩** - ZIP 文件操作

## 📄 许可证

本项目仅用于教育目的。使用者需要：
- 了解相关风险
- 遵守软件使用条款
- 承担使用责任

## 🤝 贡献

欢迎提交问题和改进建议，但请记住这是一个教育项目。

---

**再次提醒：此工具仅用于学习目的，请谨慎使用！**
