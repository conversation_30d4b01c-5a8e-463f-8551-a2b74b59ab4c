@echo off
chcp 65001 >nul
echo ========================================
echo    Cursor Reset Tool Pro - 测试脚本
echo ========================================
echo.

echo 📋 测试计划：
echo 1. 编译项目
echo 2. 运行基本功能测试
echo 3. 测试许可证系统
echo 4. 测试日志记录
echo 5. 生成测试报告
echo.

set "TEST_LOG=%~dp0test_results.log"
echo 测试开始时间: %date% %time% > "%TEST_LOG%"
echo. >> "%TEST_LOG%"

echo 🔧 步骤 1: 检查环境...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到 .NET SDK
    echo ERROR: .NET SDK not found >> "%TEST_LOG%"
    pause
    exit /b 1
)

echo ✅ .NET SDK 可用
echo SUCCESS: .NET SDK available >> "%TEST_LOG%"

echo.
echo 🔧 步骤 2: 清理和恢复依赖...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

dotnet restore
if %errorlevel% neq 0 (
    echo ❌ 依赖恢复失败
    echo ERROR: Package restore failed >> "%TEST_LOG%"
    pause
    exit /b 1
)

echo ✅ 依赖恢复成功
echo SUCCESS: Package restore completed >> "%TEST_LOG%"

echo.
echo 🔧 步骤 3: 编译项目...
dotnet build -c Debug
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    echo ERROR: Build failed >> "%TEST_LOG%"
    pause
    exit /b 1
)

echo ✅ 编译成功
echo SUCCESS: Build completed >> "%TEST_LOG%"

echo.
echo 🔧 步骤 4: 检查生成的文件...
if not exist "bin\Debug\net8.0\CursorReset.exe" (
    echo ❌ 可执行文件未生成
    echo ERROR: Executable not found >> "%TEST_LOG%"
    pause
    exit /b 1
)

echo ✅ 可执行文件已生成
echo SUCCESS: Executable generated >> "%TEST_LOG%"

echo.
echo 🔧 步骤 5: 检查管理员权限...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ 当前没有管理员权限
    echo 程序需要管理员权限才能正常运行
    echo WARNING: No administrator privileges >> "%TEST_LOG%"
    echo.
    echo 是否以管理员身份重新运行测试？(Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo 正在以管理员身份重新启动...
        powershell -Command "Start-Process '%~f0' -Verb RunAs"
        exit /b
    )
) else (
    echo ✅ 管理员权限确认
    echo SUCCESS: Administrator privileges confirmed >> "%TEST_LOG%"
)

echo.
echo 🔧 步骤 6: 创建测试配置...
mkdir "TestData" 2>nul

echo 创建测试许可证配置...
echo {> TestData\test_config.json
echo   "testLicenses": [>> TestData\test_config.json
echo     "DEMO-1234-5678-9ABC",>> TestData\test_config.json
echo     "TEST-ABCD-EFGH-IJKL",>> TestData\test_config.json
echo     "ENTERPRISE-2024-FULL">> TestData\test_config.json
echo   ],>> TestData\test_config.json
echo   "testEmail": "<EMAIL>">> TestData\test_config.json
echo }>> TestData\test_config.json

echo ✅ 测试配置创建完成
echo SUCCESS: Test configuration created >> "%TEST_LOG%"

echo.
echo 🔧 步骤 7: 运行自动化测试...
echo.
echo 📋 测试项目：
echo   ✓ 程序启动测试
echo   ✓ 许可证系统测试
echo   ✓ 日志记录测试
echo   ✓ 错误处理测试
echo.

echo 开始自动化测试...
echo AUTOMATED_TEST_START >> "%TEST_LOG%"

echo 测试 1: 程序启动和基本功能
echo | dotnet run --no-build > TestData\startup_test.log 2>&1
if %errorlevel% equ 0 (
    echo ✅ 程序启动测试通过
    echo SUCCESS: Startup test passed >> "%TEST_LOG%"
) else (
    echo ❌ 程序启动测试失败
    echo ERROR: Startup test failed >> "%TEST_LOG%"
)

echo.
echo 🔧 步骤 8: 生成测试报告...

echo ========================================> TestData\test_report.txt
echo    Cursor Reset Tool Pro - 测试报告>> TestData\test_report.txt
echo ========================================>> TestData\test_report.txt
echo.>> TestData\test_report.txt
echo 测试时间: %date% %time%>> TestData\test_report.txt
echo 测试环境: Windows %OS%>> TestData\test_report.txt
echo .NET 版本: >> TestData\test_report.txt
dotnet --version >> TestData\test_report.txt
echo.>> TestData\test_report.txt

echo 📊 测试结果汇总:>> TestData\test_report.txt
echo.>> TestData\test_report.txt

findstr "SUCCESS" "%TEST_LOG%" > TestData\success_count.tmp
findstr "ERROR" "%TEST_LOG%" > TestData\error_count.tmp
findstr "WARNING" "%TEST_LOG%" > TestData\warning_count.tmp

for /f %%i in ('type TestData\success_count.tmp ^| find /c /v ""') do set SUCCESS_COUNT=%%i
for /f %%i in ('type TestData\error_count.tmp ^| find /c /v ""') do set ERROR_COUNT=%%i
for /f %%i in ('type TestData\warning_count.tmp ^| find /c /v ""') do set WARNING_COUNT=%%i

echo ✅ 成功: %SUCCESS_COUNT% 项>> TestData\test_report.txt
echo ❌ 错误: %ERROR_COUNT% 项>> TestData\test_report.txt
echo ⚠️ 警告: %WARNING_COUNT% 项>> TestData\test_report.txt
echo.>> TestData\test_report.txt

if %ERROR_COUNT% equ 0 (
    echo 🎉 总体结果: 测试通过>> TestData\test_report.txt
    set OVERALL_RESULT=PASS
) else (
    echo 💥 总体结果: 测试失败>> TestData\test_report.txt
    set OVERALL_RESULT=FAIL
)

echo.>> TestData\test_report.txt
echo 📋 详细日志:>> TestData\test_report.txt
echo.>> TestData\test_report.txt
type "%TEST_LOG%" >> TestData\test_report.txt

echo.>> TestData\test_report.txt
echo 📁 生成的文件:>> TestData\test_report.txt
echo   - test_results.log (详细测试日志)>> TestData\test_report.txt
echo   - TestData\test_report.txt (测试报告)>> TestData\test_report.txt
echo   - TestData\startup_test.log (启动测试日志)>> TestData\test_report.txt
echo   - TestData\test_config.json (测试配置)>> TestData\test_report.txt

del TestData\success_count.tmp TestData\error_count.tmp TestData\warning_count.tmp 2>nul

echo.
echo ========================================
echo           测试完成
echo ========================================
echo.
echo 📊 测试结果:
echo   ✅ 成功: %SUCCESS_COUNT% 项
echo   ❌ 错误: %ERROR_COUNT% 项  
echo   ⚠️ 警告: %WARNING_COUNT% 项
echo.

if "%OVERALL_RESULT%"=="PASS" (
    echo 🎉 总体结果: 测试通过
    echo.
    echo 💡 下一步建议:
    echo   1. 运行 build.bat 生成发布版本
    echo   2. 手动测试所有功能
    echo   3. 检查日志记录是否正常
) else (
    echo 💥 总体结果: 测试失败
    echo.
    echo 🔧 问题排查建议:
    echo   1. 检查 test_results.log 查看详细错误
    echo   2. 确保以管理员身份运行
    echo   3. 检查 .NET 8.0 SDK 是否正确安装
)

echo.
echo 📁 测试文件位置:
echo   - 测试报告: TestData\test_report.txt
echo   - 详细日志: test_results.log
echo.

echo 是否打开测试报告？(Y/N)
set /p open_report=
if /i "%open_report%"=="Y" (
    start notepad TestData\test_report.txt
)

echo.
echo 是否现在运行程序进行手动测试？(Y/N)
set /p run_manual=
if /i "%run_manual%"=="Y" (
    echo.
    echo 启动程序进行手动测试...
    echo 💡 建议测试项目:
    echo   1. 许可证激活 (使用测试密钥)
    echo   2. 扫描功能
    echo   3. 日志查看功能
    echo   4. 错误处理
    echo.
    pause
    dotnet run --no-build
)

echo.
echo 测试脚本执行完成。
pause
