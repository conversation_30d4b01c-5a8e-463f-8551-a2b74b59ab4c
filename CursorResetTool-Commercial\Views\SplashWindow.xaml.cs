using System.Windows;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;
using CursorResetTool.Commercial.Services;
using CursorResetTool.Commercial.ViewModels;

namespace CursorResetTool.Commercial.Views
{
    public partial class SplashWindow : Window
    {
        private readonly DispatcherTimer _timer;
        private readonly IServiceProvider _serviceProvider;
        private int _progressValue = 0;
        private readonly string[] _statusMessages = {
            "正在初始化...",
            "正在加载服务...",
            "正在检查许可证...",
            "正在验证系统环境...",
            "正在准备用户界面...",
            "启动完成"
        };

        public SplashWindow()
        {
            InitializeComponent();
            
            // 从App获取服务提供者
            _serviceProvider = ((App)Application.Current).Host?.Services ?? 
                throw new InvalidOperationException("Service provider not available");

            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(500)
            };
            _timer.Tick += Timer_Tick;

            Loaded += SplashWindow_Loaded;
        }

        private async void SplashWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 设置版本信息
                var updateService = _serviceProvider.GetRequiredService<IUpdateService>();
                VersionText.Text = $"版本 {updateService.GetCurrentVersion()}";

                // 开始初始化过程
                _timer.Start();
                
                // 执行实际的初始化工作
                await InitializeApplicationAsync();
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Splash window initialization failed");
                MessageBox.Show($"初始化失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown();
            }
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            _progressValue += 20;
            ProgressBar.Value = _progressValue;

            var statusIndex = Math.Min(_progressValue / 20 - 1, _statusMessages.Length - 1);
            if (statusIndex >= 0 && statusIndex < _statusMessages.Length)
            {
                StatusText.Text = _statusMessages[statusIndex];
            }

            if (_progressValue >= 100)
            {
                _timer.Stop();
                CompleteInitialization();
            }
        }

        private async Task InitializeApplicationAsync()
        {
            try
            {
                // 延迟以显示启动画面
                await Task.Delay(500);

                // 初始化服务
                var localizationService = _serviceProvider.GetRequiredService<ILocalizationService>();
                var userService = _serviceProvider.GetRequiredService<IUserService>();
                var licenseService = _serviceProvider.GetRequiredService<ILicenseService>();

                await Task.Delay(500);

                // 加载用户设置
                var settings = userService.GetUserSettings();
                localizationService.SetLanguage(settings.Language);

                await Task.Delay(500);

                // 检查许可证
                await licenseService.ValidateLicenseAsync();

                await Task.Delay(500);

                // 检查更新（如果启用）
                if (settings.AutoCheckUpdate)
                {
                    var updateService = _serviceProvider.GetRequiredService<IUpdateService>();
                    if (updateService.GetAutoUpdateEnabled())
                    {
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await updateService.CheckForUpdatesAsync();
                            }
                            catch (Exception ex)
                            {
                                Serilog.Log.Warning(ex, "Auto update check failed");
                            }
                        });
                    }
                }

                await Task.Delay(500);

                Serilog.Log.Information("Application initialization completed");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Application initialization failed");
                throw;
            }
        }

        private void CompleteInitialization()
        {
            try
            {
                // 检查是否有有效的许可证或需要登录
                var licenseService = _serviceProvider.GetRequiredService<ILicenseService>();
                var userService = _serviceProvider.GetRequiredService<IUserService>();

                var currentLicense = licenseService.GetCurrentLicense();
                var currentUser = userService.GetCurrentUser();

                Window nextWindow;

                if (currentUser == null)
                {
                    // 没有登录用户，显示登录窗口
                    nextWindow = _serviceProvider.GetRequiredService<LoginWindow>();
                }
                else if (currentLicense == null || !currentLicense.IsValid)
                {
                    // 用户已登录但许可证无效，显示许可证激活窗口
                    nextWindow = _serviceProvider.GetRequiredService<LoginWindow>(); // 可以在登录窗口处理许可证
                }
                else
                {
                    // 用户已登录且许可证有效，直接进入主窗口
                    nextWindow = _serviceProvider.GetRequiredService<MainWindow>();
                }

                // 显示下一个窗口
                nextWindow.Show();

                // 关闭启动画面
                this.Close();
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to complete initialization");
                MessageBox.Show($"启动失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown();
            }
        }

        private void SkipButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _timer.Stop();
                
                // 直接跳转到登录窗口
                var loginWindow = _serviceProvider.GetRequiredService<LoginWindow>();
                loginWindow.Show();
                
                this.Close();
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to skip splash screen");
                MessageBox.Show($"跳过失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 显示设置窗口
                var settingsWindow = _serviceProvider.GetRequiredService<SettingsWindow>();
                settingsWindow.Owner = this;
                settingsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to open settings window");
                MessageBox.Show($"打开设置失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }
    }
}
