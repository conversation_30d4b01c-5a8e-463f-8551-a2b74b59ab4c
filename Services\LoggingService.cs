using System;
using System.IO;
using System.Text;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace CursorReset.Services
{
    public enum LogLevel
    {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3,
        Critical = 4
    }

    public static class LoggingService
    {
        private static readonly string LogDirectory;
        private static readonly string LogFileName;
        private static readonly object LogLock = new();
        private static LogLevel MinimumLogLevel = LogLevel.Info;

        static LoggingService()
        {
            LogDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "CursorResetTool", "logs");
            
            LogFileName = $"app_{DateTime.Now:yyyyMMdd}.log";
            
            try
            {
                Directory.CreateDirectory(LogDirectory);
            }
            catch
            {
                // 如果无法创建日志目录，使用临时目录
                LogDirectory = Path.GetTempPath();
            }
        }

        public static void SetLogLevel(LogLevel level)
        {
            MinimumLogLevel = level;
        }

        public static void LogDebug(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Debug, message, null, memberName, filePath, lineNumber);
        }

        public static void LogInfo(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Info, message, null, memberName, filePath, lineNumber);
        }

        public static void LogWarning(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Warning, message, null, memberName, filePath, lineNumber);
        }

        public static void LogError(string message, Exception exception = null, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Error, message, exception, memberName, filePath, lineNumber);
        }

        public static void LogCritical(string message, Exception exception = null, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Critical, message, exception, memberName, filePath, lineNumber);
        }

        private static void Log(LogLevel level, string message, Exception exception, string memberName, string filePath, int lineNumber)
        {
            if (level < MinimumLogLevel) return;

            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var fileName = Path.GetFileName(filePath);
                var logEntry = new StringBuilder();

                // 基本信息
                logEntry.AppendLine($"[{timestamp}] [{level}] {message}");
                logEntry.AppendLine($"    Location: {fileName}::{memberName}:{lineNumber}");

                // 异常信息
                if (exception != null)
                {
                    logEntry.AppendLine($"    Exception: {exception.GetType().Name}");
                    logEntry.AppendLine($"    Message: {exception.Message}");
                    logEntry.AppendLine($"    StackTrace: {exception.StackTrace}");
                    
                    // 内部异常
                    var innerEx = exception.InnerException;
                    while (innerEx != null)
                    {
                        logEntry.AppendLine($"    InnerException: {innerEx.GetType().Name}");
                        logEntry.AppendLine($"    InnerMessage: {innerEx.Message}");
                        innerEx = innerEx.InnerException;
                    }
                }

                // 系统信息（仅在错误和严重级别记录）
                if (level >= LogLevel.Error)
                {
                    logEntry.AppendLine($"    OS: {Environment.OSVersion}");
                    logEntry.AppendLine($"    .NET: {Environment.Version}");
                    logEntry.AppendLine($"    Machine: {Environment.MachineName}");
                    logEntry.AppendLine($"    User: {Environment.UserName}");
                    logEntry.AppendLine($"    Memory: {GC.GetTotalMemory(false) / 1024 / 1024} MB");
                }

                logEntry.AppendLine(new string('-', 80));

                // 写入文件
                lock (LogLock)
                {
                    var logFilePath = Path.Combine(LogDirectory, LogFileName);
                    File.AppendAllText(logFilePath, logEntry.ToString(), Encoding.UTF8);
                }

                // 同时输出到控制台（错误级别）
                if (level >= LogLevel.Error)
                {
                    var originalColor = Console.ForegroundColor;
                    Console.ForegroundColor = level == LogLevel.Critical ? ConsoleColor.Red : ConsoleColor.Yellow;
                    Console.WriteLine($"[{level}] {message}");
                    if (exception != null)
                    {
                        Console.WriteLine($"错误详情: {exception.Message}");
                    }
                    Console.ForegroundColor = originalColor;
                }

                // 写入 Windows 事件日志（仅严重错误）
                if (level == LogLevel.Critical)
                {
                    WriteToEventLog(message, exception);
                }
            }
            catch
            {
                // 日志记录失败时不抛出异常，避免影响主程序
            }
        }

        private static void WriteToEventLog(string message, Exception exception)
        {
            try
            {
                using var eventLog = new EventLog("Application");
                eventLog.Source = "CursorResetTool";
                
                var logMessage = $"{message}";
                if (exception != null)
                {
                    logMessage += $"\n\n异常信息:\n{exception}";
                }
                
                eventLog.WriteEntry(logMessage, EventLogEntryType.Error);
            }
            catch
            {
                // 忽略事件日志写入失败
            }
        }

        public static void LogPerformance(string operation, TimeSpan duration, [CallerMemberName] string memberName = "")
        {
            LogInfo($"Performance: {operation} completed in {duration.TotalMilliseconds:F2}ms", memberName);
        }

        public static void LogUserAction(string action, string details = "")
        {
            var message = $"User Action: {action}";
            if (!string.IsNullOrEmpty(details))
            {
                message += $" - {details}";
            }
            LogInfo(message);
        }

        public static void LogSystemInfo()
        {
            LogInfo("=== System Information ===");
            LogInfo($"OS: {Environment.OSVersion}");
            LogInfo($".NET Version: {Environment.Version}");
            LogInfo($"Machine Name: {Environment.MachineName}");
            LogInfo($"User Name: {Environment.UserName}");
            LogInfo($"Working Directory: {Environment.CurrentDirectory}");
            LogInfo($"Is 64-bit OS: {Environment.Is64BitOperatingSystem}");
            LogInfo($"Is 64-bit Process: {Environment.Is64BitProcess}");
            LogInfo($"Processor Count: {Environment.ProcessorCount}");
            LogInfo($"Total Memory: {GC.GetTotalMemory(false) / 1024 / 1024} MB");
            LogInfo("=== End System Information ===");
        }

        public static string GetLogDirectory()
        {
            return LogDirectory;
        }

        public static void OpenLogDirectory()
        {
            try
            {
                Process.Start("explorer.exe", LogDirectory);
            }
            catch (Exception ex)
            {
                LogError("Failed to open log directory", ex);
            }
        }

        public static void CleanOldLogs(int daysToKeep = 30)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var logFiles = Directory.GetFiles(LogDirectory, "app_*.log");
                
                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                        LogInfo($"Deleted old log file: {Path.GetFileName(logFile)}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("Failed to clean old logs", ex);
            }
        }

        public static void LogApplicationStart()
        {
            LogInfo("=== Application Started ===");
            LogSystemInfo();
            
            // 清理旧日志
            CleanOldLogs();
        }

        public static void LogApplicationEnd()
        {
            LogInfo("=== Application Ended ===");
        }
    }

    // 性能监控辅助类
    public class PerformanceTimer : IDisposable
    {
        private readonly string _operation;
        private readonly Stopwatch _stopwatch;
        private bool _disposed = false;

        public PerformanceTimer(string operation)
        {
            _operation = operation;
            _stopwatch = Stopwatch.StartNew();
            LoggingService.LogDebug($"Started: {operation}");
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _stopwatch.Stop();
                LoggingService.LogPerformance(_operation, _stopwatch.Elapsed);
                _disposed = true;
            }
        }
    }
}
