using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace CursorResetTool.Demo
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            InitializeSystemInfo();
        }

        private void InitializeSystemInfo()
        {
            OSText.Text = $"操作系统: {Environment.OSVersion}";
            ArchText.Text = $"架构: {(Environment.Is64BitOperatingSystem ? "x64" : "x86")}";
        }

        private void ScanButton_Click(object sender, RoutedEventArgs e)
        {
            UpdateStatus("正在扫描 Cursor 数据...");
            OperationTitle.Text = "扫描 Cursor 数据";
            
            ShowProgress();
            
            // 模拟扫描过程
            Task.Run(async () =>
            {
                for (int i = 0; i <= 100; i += 10)
                {
                    Dispatcher.Invoke(() => OperationProgress.Value = i);
                    await Task.Delay(200);
                }
                
                Dispatcher.Invoke(() =>
                {
                    HideProgress();
                    ShowScanResults();
                    UpdateStatus("扫描完成 - 发现 15 个相关项目");
                });
            });
        }

        private void BackupButton_Click(object sender, RoutedEventArgs e)
        {
            UpdateStatus("正在备份 Cursor 数据...");
            OperationTitle.Text = "备份 Cursor 数据";
            
            ShowProgress();
            
            // 模拟备份过程
            Task.Run(async () =>
            {
                for (int i = 0; i <= 100; i += 5)
                {
                    Dispatcher.Invoke(() => OperationProgress.Value = i);
                    await Task.Delay(100);
                }
                
                Dispatcher.Invoke(() =>
                {
                    HideProgress();
                    ShowBackupResults();
                    UpdateStatus("备份完成 - 已保存到桌面");
                });
            });
        }

        private void CleanButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "确定要清理 Cursor 数据吗？\n\n此操作将：\n• 删除所有 Cursor 相关文件\n• 清理注册表项\n• 关闭相关进程\n\n建议在清理前先进行备份！",
                "确认清理",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result != MessageBoxResult.Yes)
                return;

            UpdateStatus("正在清理 Cursor 数据...");
            OperationTitle.Text = "清理 Cursor 数据";
            
            ShowProgress();
            
            // 模拟清理过程
            Task.Run(async () =>
            {
                for (int i = 0; i <= 100; i += 8)
                {
                    Dispatcher.Invoke(() => OperationProgress.Value = i);
                    await Task.Delay(150);
                }
                
                Dispatcher.Invoke(() =>
                {
                    HideProgress();
                    ShowCleanResults();
                    UpdateStatus("清理完成 - 建议重启系统");
                });
            });
        }

        private void RestoreButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "确定要恢复备份数据吗？\n\n此操作将从最新备份恢复所有 Cursor 数据。",
                "确认恢复",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes)
                return;

            UpdateStatus("正在恢复 Cursor 数据...");
            OperationTitle.Text = "恢复 Cursor 数据";
            
            ShowProgress();
            
            // 模拟恢复过程
            Task.Run(async () =>
            {
                for (int i = 0; i <= 100; i += 12)
                {
                    Dispatcher.Invoke(() => OperationProgress.Value = i);
                    await Task.Delay(120);
                }
                
                Dispatcher.Invoke(() =>
                {
                    HideProgress();
                    ShowRestoreResults();
                    UpdateStatus("恢复完成");
                });
            });
        }

        private void AboutButton_Click(object sender, RoutedEventArgs e)
        {
            var aboutText = @"尚书_CAR_cursor重置系统 - 演示版

版本：2.0.0
开发：尚书科技
技术：WPF + Material Design + .NET 8.0

功能特点：
• 现代化的用户界面设计
• 完整的 Cursor 数据管理
• 企业级安全和许可证系统
• 一机一码防盗版保护
• 多语言支持和自动更新

这是一个演示版本，展示了完整的商用软件架构和设计。

Copyright © 2024 尚书科技
All rights reserved.";

            MessageBox.Show(aboutText, "关于", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ShowProgress()
        {
            OperationProgress.Visibility = Visibility.Visible;
            OperationProgress.Value = 0;
            OperationProgress.IsIndeterminate = false;
        }

        private void HideProgress()
        {
            OperationProgress.Visibility = Visibility.Collapsed;
        }

        private void UpdateStatus(string message)
        {
            StatusText.Text = message;
        }

        private void ShowScanResults()
        {
            ContentPanel.Children.Clear();
            
            var resultText = @"扫描完成！

发现的项目：
• 文件和目录: 12 个
• 注册表项: 3 个
• 总大小: 245.6 MB

详细信息：
• C:\Users\<USER>\AppData\Roaming\Cursor (目录)
• C:\Users\<USER>\AppData\Local\Cursor (目录)
• C:\Users\<USER>\.cursor (目录)
• HKEY_CURRENT_USER\Software\Cursor (注册表)
• 桌面快捷方式 Cursor.lnk
• 开始菜单快捷方式
... 还有 9 个其他项目

建议：
• 在清理前请先进行备份
• 确保 Cursor 程序已完全关闭
• 清理后建议重启系统";

            var resultBlock = new TextBlock
            {
                Text = resultText,
                Style = (Style)FindResource("BodyTextStyle"),
                TextWrapping = TextWrapping.Wrap,
                FontFamily = (FontFamily)FindResource("MonospaceFont"),
                FontSize = 12,
                Background = (Brush)FindResource("BackgroundBrush"),
                Padding = new Thickness(15),
                Margin = new Thickness(0, 0, 0, 10)
            };

            var border = new Border
            {
                Child = resultBlock,
                CornerRadius = new CornerRadius(8),
                Background = (Brush)FindResource("BackgroundBrush")
            };

            ContentPanel.Children.Add(border);
        }

        private void ShowBackupResults()
        {
            ContentPanel.Children.Clear();
            
            var resultText = @"备份成功！

备份信息：
• 备份文件: CursorBackup_20241219_143022.zip
• 保存位置: C:\Users\<USER>\Desktop\CursorBackup\
• 备份大小: 245.6 MB
• 备份项目: 15 个

包含内容：
• 所有配置文件和用户数据
• 扩展插件和主题设置
• 工作区和项目配置
• 注册表相关项目

建议：
• 请妥善保管备份文件
• 清理前请确认备份完整
• 如需恢复，请使用恢复功能

备份已完成，现在可以安全地进行清理操作。";

            var resultBlock = new TextBlock
            {
                Text = resultText,
                Style = (Style)FindResource("BodyTextStyle"),
                TextWrapping = TextWrapping.Wrap,
                FontSize = 12,
                Background = (Brush)FindResource("BackgroundBrush"),
                Padding = new Thickness(15),
                Margin = new Thickness(0, 0, 0, 10)
            };

            var border = new Border
            {
                Child = resultBlock,
                CornerRadius = new CornerRadius(8),
                Background = (Brush)FindResource("SuccessBrush"),
                Opacity = 0.1
            };

            ContentPanel.Children.Add(border);
        }

        private void ShowCleanResults()
        {
            ContentPanel.Children.Clear();
            
            var resultText = @"清理完成！

已清理项目：
• 删除文件和目录: 12 个
• 清理注册表项: 3 个
• 释放磁盘空间: 245.6 MB
• 关闭相关进程: 2 个

清理详情：
• ✓ Cursor 主程序目录
• ✓ 用户配置和数据目录
• ✓ 临时文件和缓存
• ✓ 注册表项和文件关联
• ✓ 桌面和开始菜单快捷方式

重要提示：
• 清理操作已完成
• 建议立即重启系统以确保清理完全生效
• 如需重新安装 Cursor，请从官网下载最新版本
• 如遇问题，可使用备份文件恢复

Cursor 已完全从系统中移除！";

            var resultBlock = new TextBlock
            {
                Text = resultText,
                Style = (Style)FindResource("BodyTextStyle"),
                TextWrapping = TextWrapping.Wrap,
                FontSize = 12,
                Foreground = (Brush)FindResource("SuccessBrush"),
                FontWeight = FontWeights.Medium,
                Background = (Brush)FindResource("BackgroundBrush"),
                Padding = new Thickness(15),
                Margin = new Thickness(0, 0, 0, 10)
            };

            var border = new Border
            {
                Child = resultBlock,
                CornerRadius = new CornerRadius(8),
                Background = (Brush)FindResource("BackgroundBrush")
            };

            ContentPanel.Children.Add(border);
        }

        private void ShowRestoreResults()
        {
            ContentPanel.Children.Clear();
            
            var resultText = @"恢复完成！

恢复信息：
• 恢复文件: CursorBackup_20241219_143022.zip
• 恢复项目: 15 个
• 恢复大小: 245.6 MB

已恢复内容：
• ✓ 所有配置文件和用户数据
• ✓ 扩展插件和主题设置
• ✓ 工作区和项目配置
• ✓ 注册表相关项目
• ✓ 桌面和开始菜单快捷方式

状态检查：
• Cursor 程序文件: 已恢复
• 用户配置: 已恢复
• 扩展插件: 已恢复
• 文件关联: 已恢复

提示：
• 数据恢复成功完成
• Cursor 现在可以正常使用
• 所有设置和配置已恢复到备份时的状态

恢复操作已完成，Cursor 已成功恢复！";

            var resultBlock = new TextBlock
            {
                Text = resultText,
                Style = (Style)FindResource("BodyTextStyle"),
                TextWrapping = TextWrapping.Wrap,
                FontSize = 12,
                Foreground = (Brush)FindResource("SuccessBrush"),
                Background = (Brush)FindResource("BackgroundBrush"),
                Padding = new Thickness(15),
                Margin = new Thickness(0, 0, 0, 10)
            };

            var border = new Border
            {
                Child = resultBlock,
                CornerRadius = new CornerRadius(8),
                Background = (Brush)FindResource("BackgroundBrush")
            };

            ContentPanel.Children.Add(border);
        }
    }
}
