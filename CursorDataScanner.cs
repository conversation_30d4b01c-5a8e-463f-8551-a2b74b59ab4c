using System;
using System.IO;
using System.Collections.Generic;
using Microsoft.Win32;

namespace CursorReset
{
    public class CursorDataScanner
    {
        private readonly List<string> _foundFiles = new();
        private readonly List<string> _foundRegistryKeys = new();

        public void ScanAll()
        {
            Console.WriteLine("📁 扫描文件系统...");
            ScanFileSystem();
            
            Console.WriteLine("\n🗃️ 扫描注册表...");
            ScanRegistry();
            
            Console.WriteLine("\n📊 扫描结果汇总:");
            ShowResults();
        }

        private void ScanFileSystem()
        {
            var locations = GetCursorFileLocations();
            
            foreach (var location in locations)
            {
                try
                {
                    if (Directory.Exists(location.Path))
                    {
                        Console.WriteLine($"✅ 找到: {location.Description}");
                        Console.WriteLine($"   路径: {location.Path}");
                        _foundFiles.Add(location.Path);
                        
                        // 显示目录大小
                        var size = GetDirectorySize(location.Path);
                        Console.WriteLine($"   大小: {FormatBytes(size)}");
                    }
                    else if (File.Exists(location.Path))
                    {
                        Console.WriteLine($"✅ 找到文件: {location.Description}");
                        Console.WriteLine($"   路径: {location.Path}");
                        _foundFiles.Add(location.Path);
                        
                        var fileInfo = new FileInfo(location.Path);
                        Console.WriteLine($"   大小: {FormatBytes(fileInfo.Length)}");
                    }
                    else
                    {
                        Console.WriteLine($"❌ 未找到: {location.Description}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 扫描错误 {location.Description}: {ex.Message}");
                }
            }
        }

        private void ScanRegistry()
        {
            var registryPaths = GetCursorRegistryPaths();
            
            foreach (var regPath in registryPaths)
            {
                try
                {
                    using var key = Registry.CurrentUser.OpenSubKey(regPath.Path);
                    if (key != null)
                    {
                        Console.WriteLine($"✅ 找到注册表项: {regPath.Description}");
                        Console.WriteLine($"   路径: HKEY_CURRENT_USER\\{regPath.Path}");
                        _foundRegistryKeys.Add(regPath.Path);
                        
                        // 显示子键数量
                        var subKeyCount = key.SubKeyCount;
                        var valueCount = key.ValueCount;
                        Console.WriteLine($"   子键: {subKeyCount}, 值: {valueCount}");
                    }
                    else
                    {
                        Console.WriteLine($"❌ 未找到注册表项: {regPath.Description}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 注册表扫描错误 {regPath.Description}: {ex.Message}");
                }
            }
        }

        private List<(string Path, string Description)> GetCursorFileLocations()
        {
            var userProfile = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            var appData = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var localAppData = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            var programFiles = Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles);
            var programFilesX86 = Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86);

            return new List<(string, string)>
            {
                (Path.Combine(appData, "Cursor"), "Cursor 应用数据"),
                (Path.Combine(localAppData, "Cursor"), "Cursor 本地数据"),
                (Path.Combine(localAppData, "cursor-updater"), "Cursor 更新器"),
                (Path.Combine(userProfile, ".cursor"), "Cursor 用户配置"),
                (Path.Combine(userProfile, ".cursor-server"), "Cursor 服务器配置"),
                (Path.Combine(programFiles, "Cursor"), "Cursor 程序文件"),
                (Path.Combine(programFilesX86, "Cursor"), "Cursor 程序文件 (x86)"),
                (Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "Cursor.lnk"), "桌面快捷方式"),
                (Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.StartMenu), "Programs", "Cursor.lnk"), "开始菜单快捷方式")
            };
        }

        private List<(string Path, string Description)> GetCursorRegistryPaths()
        {
            return new List<(string, string)>
            {
                (@"Software\Cursor", "Cursor 主配置"),
                (@"Software\Microsoft\Windows\CurrentVersion\Uninstall\Cursor", "Cursor 卸载信息"),
                (@"Software\Classes\Applications\Cursor.exe", "Cursor 应用程序关联"),
                (@"Software\Classes\.cursor", "Cursor 文件关联"),
                (@"Software\RegisteredApplications", "注册的应用程序"),
                (@"Software\Microsoft\Windows\CurrentVersion\ApplicationAssociationToasts", "应用程序关联提示")
            };
        }

        private long GetDirectorySize(string path)
        {
            try
            {
                var dirInfo = new DirectoryInfo(path);
                long size = 0;
                
                foreach (var file in dirInfo.GetFiles("*", SearchOption.AllDirectories))
                {
                    size += file.Length;
                }
                
                return size;
            }
            catch
            {
                return 0;
            }
        }

        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }

        private void ShowResults()
        {
            Console.WriteLine($"📁 找到 {_foundFiles.Count} 个文件/目录");
            Console.WriteLine($"🗃️ 找到 {_foundRegistryKeys.Count} 个注册表项");

            if (_foundFiles.Count > 0 || _foundRegistryKeys.Count > 0)
            {
                Console.WriteLine("\n💡 提示：可以使用备份功能保存这些数据，然后使用清理功能删除它们。");
            }
        }

        public List<string> GetFoundFiles() => new(_foundFiles);
        public List<string> GetFoundRegistryKeys() => new(_foundRegistryKeys);
    }
}
