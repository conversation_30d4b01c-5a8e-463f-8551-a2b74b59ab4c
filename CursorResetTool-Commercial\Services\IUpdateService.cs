using System.Diagnostics;

namespace CursorResetTool.Commercial.Services
{
    /// <summary>
    /// 更新服务接口
    /// </summary>
    public interface IUpdateService
    {
        /// <summary>
        /// 检查更新
        /// </summary>
        Task<UpdateInfo?> CheckForUpdatesAsync();

        /// <summary>
        /// 下载更新
        /// </summary>
        Task<(bool success, string filePath)> DownloadUpdateAsync(UpdateInfo updateInfo, IProgress<int>? progress = null);

        /// <summary>
        /// 安装更新
        /// </summary>
        Task<bool> InstallUpdateAsync(string updateFilePath);

        /// <summary>
        /// 获取当前版本
        /// </summary>
        string GetCurrentVersion();

        /// <summary>
        /// 比较版本
        /// </summary>
        bool IsNewerVersion(string currentVersion, string newVersion);

        /// <summary>
        /// 设置自动更新
        /// </summary>
        void SetAutoUpdate(bool enabled);

        /// <summary>
        /// 获取自动更新状态
        /// </summary>
        bool GetAutoUpdateEnabled();
    }

    /// <summary>
    /// 更新信息
    /// </summary>
    public class UpdateInfo
    {
        public string Version { get; set; } = string.Empty;
        public string DownloadUrl { get; set; } = string.Empty;
        public string ReleaseNotes { get; set; } = string.Empty;
        public DateTime ReleaseDate { get; set; }
        public long FileSize { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string Checksum { get; set; } = string.Empty;
        public bool IsRequired { get; set; }
        public List<string> SupportedPlatforms { get; set; } = new();
    }

    /// <summary>
    /// 更新服务实现
    /// </summary>
    public class UpdateService : IUpdateService
    {
        private readonly INetworkService _networkService;
        private readonly ISecurityService _securityService;
        private readonly string _updateDirectory;
        private bool _autoUpdateEnabled;

        public UpdateService(INetworkService networkService, ISecurityService securityService)
        {
            _networkService = networkService;
            _securityService = securityService;
            _updateDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "尚书CAR", "Updates");
            Directory.CreateDirectory(_updateDirectory);
            
            // 从配置文件读取自动更新设置
            _autoUpdateEnabled = LoadAutoUpdateSetting();
        }

        public async Task<UpdateInfo?> CheckForUpdatesAsync()
        {
            try
            {
                var (hasUpdate, version, downloadUrl, releaseNotes) = await _networkService.CheckForUpdatesAsync();
                
                if (hasUpdate && !string.IsNullOrEmpty(version))
                {
                    var currentVersion = GetCurrentVersion();
                    if (IsNewerVersion(currentVersion, version))
                    {
                        return new UpdateInfo
                        {
                            Version = version,
                            DownloadUrl = downloadUrl,
                            ReleaseNotes = releaseNotes,
                            ReleaseDate = DateTime.Now,
                            FileName = Path.GetFileName(new Uri(downloadUrl).LocalPath),
                            SupportedPlatforms = new List<string> { "Windows" }
                        };
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to check for updates");
                return null;
            }
        }

        public async Task<(bool success, string filePath)> DownloadUpdateAsync(UpdateInfo updateInfo, IProgress<int>? progress = null)
        {
            try
            {
                if (string.IsNullOrEmpty(updateInfo.DownloadUrl))
                    return (false, "");

                var fileName = updateInfo.FileName;
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = $"Update_{updateInfo.Version}_{DateTime.Now:yyyyMMdd_HHmmss}.exe";
                }

                var filePath = Path.Combine(_updateDirectory, fileName);

                // 如果文件已存在，先删除
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                var result = await _networkService.DownloadUpdateAsync(updateInfo.DownloadUrl, progress);
                
                if (result.success && !string.IsNullOrEmpty(result.filePath))
                {
                    // 移动文件到更新目录
                    File.Move(result.filePath, filePath);
                    
                    // 验证文件完整性（如果有校验和）
                    if (!string.IsNullOrEmpty(updateInfo.Checksum))
                    {
                        var fileChecksum = CalculateFileChecksum(filePath);
                        if (!string.Equals(fileChecksum, updateInfo.Checksum, StringComparison.OrdinalIgnoreCase))
                        {
                            File.Delete(filePath);
                            return (false, "文件校验失败");
                        }
                    }

                    return (true, filePath);
                }

                return (false, "下载失败");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to download update");
                return (false, $"下载失败：{ex.Message}");
            }
        }

        public async Task<bool> InstallUpdateAsync(string updateFilePath)
        {
            try
            {
                if (!File.Exists(updateFilePath))
                    return false;

                // 创建更新脚本
                var scriptPath = CreateUpdateScript(updateFilePath);
                
                if (string.IsNullOrEmpty(scriptPath))
                    return false;

                // 启动更新脚本
                var startInfo = new ProcessStartInfo
                {
                    FileName = scriptPath,
                    UseShellExecute = true,
                    Verb = "runas", // 请求管理员权限
                    CreateNoWindow = false
                };

                Process.Start(startInfo);

                // 退出当前应用程序
                await Task.Delay(1000); // 给脚本一点时间启动
                Environment.Exit(0);

                return true;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to install update");
                return false;
            }
        }

        public string GetCurrentVersion()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "*******";
            }
            catch
            {
                return "*******";
            }
        }

        public bool IsNewerVersion(string currentVersion, string newVersion)
        {
            try
            {
                var current = new Version(currentVersion);
                var newer = new Version(newVersion);
                return newer > current;
            }
            catch
            {
                return false;
            }
        }

        public void SetAutoUpdate(bool enabled)
        {
            _autoUpdateEnabled = enabled;
            SaveAutoUpdateSetting(enabled);
        }

        public bool GetAutoUpdateEnabled()
        {
            return _autoUpdateEnabled;
        }

        private string CreateUpdateScript(string updateFilePath)
        {
            try
            {
                var scriptPath = Path.Combine(_updateDirectory, "update.bat");
                var currentExePath = Process.GetCurrentProcess().MainModule?.FileName ?? "";
                var currentExeDir = Path.GetDirectoryName(currentExePath) ?? "";
                var currentExeName = Path.GetFileName(currentExePath) ?? "";
                var backupExePath = Path.Combine(currentExeDir, $"{Path.GetFileNameWithoutExtension(currentExeName)}_backup.exe");

                var scriptContent = $@"@echo off
chcp 65001 >nul
title 尚书CAR更新程序

echo ========================================
echo    尚书CAR Cursor重置系统 - 更新程序
echo ========================================
echo.

echo 正在准备更新...
timeout /t 3 /nobreak >nul

echo 等待主程序退出...
:wait_loop
tasklist /fi ""imagename={currentExeName}"" 2>nul | find /i ""{currentExeName}"" >nul
if not errorlevel 1 (
    timeout /t 1 /nobreak >nul
    goto wait_loop
)

echo 主程序已退出，开始更新...

REM 备份当前版本
if exist ""{currentExePath}"" (
    echo 备份当前版本...
    copy ""{currentExePath}"" ""{backupExePath}"" >nul
    if errorlevel 1 (
        echo 备份失败！
        pause
        exit /b 1
    )
)

REM 安装新版本
echo 安装新版本...
copy ""{updateFilePath}"" ""{currentExePath}"" >nul
if errorlevel 1 (
    echo 安装失败！正在恢复备份...
    if exist ""{backupExePath}"" (
        copy ""{backupExePath}"" ""{currentExePath}"" >nul
    )
    pause
    exit /b 1
)

echo 更新完成！

REM 启动新版本
echo 启动新版本...
start """" ""{currentExePath}""

REM 清理
timeout /t 2 /nobreak >nul
if exist ""{backupExePath}"" del ""{backupExePath}"" >nul
if exist ""{updateFilePath}"" del ""{updateFilePath}"" >nul
del ""%~f0"" >nul

exit
";

                File.WriteAllText(scriptPath, scriptContent, System.Text.Encoding.UTF8);
                return scriptPath;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to create update script");
                return "";
            }
        }

        private string CalculateFileChecksum(string filePath)
        {
            try
            {
                using var sha256 = System.Security.Cryptography.SHA256.Create();
                using var stream = File.OpenRead(filePath);
                var hash = sha256.ComputeHash(stream);
                return Convert.ToHexString(hash);
            }
            catch
            {
                return "";
            }
        }

        private bool LoadAutoUpdateSetting()
        {
            try
            {
                var settingsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "尚书CAR", "settings.json");
                if (File.Exists(settingsPath))
                {
                    var json = File.ReadAllText(settingsPath);
                    var settings = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(json);
                    return settings?.AutoUpdate ?? true;
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Warning(ex, "Failed to load auto update setting");
            }
            return true; // 默认启用自动更新
        }

        private void SaveAutoUpdateSetting(bool enabled)
        {
            try
            {
                var settingsDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "尚书CAR");
                Directory.CreateDirectory(settingsDir);
                
                var settingsPath = Path.Combine(settingsDir, "settings.json");
                
                var settings = new { AutoUpdate = enabled };
                
                if (File.Exists(settingsPath))
                {
                    var existingJson = File.ReadAllText(settingsPath);
                    var existingSettings = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(existingJson) ?? new { };
                    existingSettings.AutoUpdate = enabled;
                    settings = existingSettings;
                }

                var json = Newtonsoft.Json.JsonConvert.SerializeObject(settings, Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(settingsPath, json);
            }
            catch (Exception ex)
            {
                Serilog.Log.Warning(ex, "Failed to save auto update setting");
            }
        }
    }
}
