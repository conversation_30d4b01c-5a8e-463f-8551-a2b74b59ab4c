# 尚书_CAR_cursor重置系统 - 商用版

## 🎯 项目简介

**尚书_CAR_cursor重置系统** 是一个基于 WPF 的现代化桌面应用程序，采用 Material Design 设计风格，具备完整的用户管理、许可证管理和一机一码防盗版机制。

### ✨ 核心特性

- 🎨 **现代化UI** - Material Design + Fluent Design 风格
- 👥 **完整用户系统** - 注册、登录、会员管理
- 🔐 **企业级许可证** - 网络验证、硬件绑定、防盗版
- 🛡️ **一机一码授权** - 基于硬件指纹的设备绑定
- 🌐 **多语言支持** - 中英文界面切换
- 🔄 **自动更新** - 在线更新检查和安装
- 📊 **完整日志** - 多级别日志记录和监控

## 🚀 快速开始

### 系统要求

- **操作系统**: Windows 10/11 (64位推荐)
- **开发环境**: Visual Studio 2022 或 VS Code
- **框架**: .NET 8.0 SDK
- **权限**: 管理员权限

### 安装依赖

```bash
# 克隆项目
git clone https://github.com/your-repo/CursorResetTool-Commercial.git
cd CursorResetTool-Commercial

# 恢复 NuGet 包
dotnet restore

# 构建项目
dotnet build
```

### 运行项目

```bash
# 开发模式运行
dotnet run

# 或者在 Visual Studio 中按 F5
```

## 🏗️ 项目架构

### 技术栈

- **.NET 8.0** - 最新的 .NET 框架
- **WPF** - Windows Presentation Foundation
- **MVVM** - Model-View-ViewModel 架构模式
- **Material Design** - MaterialDesignInXamlToolkit
- **依赖注入** - Microsoft.Extensions.DependencyInjection
- **日志记录** - Serilog
- **加密安全** - BCrypt, AES, RSA

### 目录结构

```
CursorResetTool-Commercial/
├── Models/                     # 数据模型
├── Services/                   # 业务服务
├── Views/                      # 视图界面
├── ViewModels/                 # 视图模型
├── Controls/                   # 自定义控件
├── Styles/                     # 样式资源
├── Resources/                  # 资源文件
├── Localization/              # 本地化资源
├── App.xaml                   # 应用程序入口
└── README.md                  # 项目说明
```

## 🔧 核心功能

### 1. 用户管理系统

```csharp
// 用户注册
var result = await userService.RegisterAsync(username, email, password);

// 用户登录
var (success, user, message) = await userService.LoginAsync(usernameOrEmail, password);

// 密码重置
var result = await userService.ResetPasswordAsync(email);
```

### 2. 许可证管理

```csharp
// 在线激活许可证
var result = await licenseService.ActivateLicenseOnlineAsync(licenseKey, userEmail);

// 验证许可证
var (isValid, license, message) = await licenseService.ValidateLicenseAsync();

// 生成试用许可证
var result = await licenseService.GenerateTrialLicenseAsync(userEmail);
```

### 3. 一机一码防盗版

```csharp
// 生成机器码
string machineCode = machineCodeService.GenerateMachineCode();

// 生成硬件指纹
string fingerprint = machineCodeService.GenerateHardwareFingerprint();

// 验证设备授权
bool isAuthorized = licenseService.IsDeviceAuthorized();
```

### 4. Cursor 重置功能

```csharp
// 扫描 Cursor 数据
var scanResult = await cursorResetService.ScanCursorDataAsync();

// 备份数据
var (success, backupPath, message) = await cursorResetService.BackupCursorDataAsync();

// 清理数据
var (success, message) = await cursorResetService.CleanCursorDataAsync();

// 恢复数据
var (success, message) = await cursorResetService.RestoreCursorDataAsync(backupPath);
```

## 🎨 UI 设计

### Material Design 风格

应用程序采用 Google Material Design 设计规范，提供现代化的用户体验：

- **卡片式布局** - 清晰的信息层次
- **浮动操作按钮** - 主要操作突出显示
- **材质动画** - 流畅的过渡效果
- **阴影效果** - 立体的视觉层次
- **一致的色彩** - 统一的品牌色彩

### 自定义样式

```xml
<!-- 主要按钮样式 -->
<Style x:Key="PrimaryButtonStyle" TargetType="Button">
    <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
    <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="20"/>
</Style>

<!-- 卡片样式 -->
<Style x:Key="CardStyle" TargetType="Border">
    <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
    <Setter Property="CornerRadius" Value="8"/>
</Style>
```

## 🔐 安全机制

### 一机一码实现

```csharp
public class MachineCodeService
{
    public string GenerateMachineCode()
    {
        var components = new List<string>
        {
            GetCpuInfo(),           // CPU 信息
            GetMotherboardInfo(),   // 主板信息
            GetHardDiskInfo(),      // 硬盘信息
            GetMemoryInfo(),        // 内存信息
            GetNetworkAdapterInfo() // 网卡信息
        };

        var combinedInfo = string.Join("|", components);
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(combinedInfo));
        return Convert.ToBase64String(hash);
    }
}
```

### 许可证加密

```csharp
public class LicenseService
{
    public async Task<bool> SaveLicenseLocallyAsync(License license)
    {
        var json = JsonConvert.SerializeObject(license);
        var encryptedJson = securityService.EncryptAES(json, "LicenseData2024");
        await File.WriteAllTextAsync(licensePath, encryptedJson);
        return true;
    }
}
```

## 🌐 多语言支持

### 支持的语言

- 🇨🇳 简体中文 (zh-CN)
- 🇺🇸 英语 (en-US)
- 🇹🇼 繁体中文 (zh-TW)
- 🇯🇵 日语 (ja-JP)
- 🇰🇷 韩语 (ko-KR)

### 使用方法

```csharp
// 设置语言
localizationService.SetLanguage("zh-CN");

// 获取本地化字符串
string text = localizationService.GetString("Login");
string formatted = localizationService.GetString("WelcomeMessage", userName);
```

## 📦 构建和部署

### 开发环境构建

```bash
# Debug 构建
dotnet build -c Debug

# Release 构建
dotnet build -c Release
```

### 发布单文件版本

```bash
# 64位版本
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true

# 32位版本
dotnet publish -c Release -r win-x86 --self-contained true -p:PublishSingleFile=true
```

### 创建安装包

```bash
# 使用 WiX 或 Inno Setup 创建安装程序
# 或者使用 MSIX 打包
dotnet pack
```

## 🧪 测试

### 单元测试

```bash
# 运行所有测试
dotnet test

# 运行特定测试
dotnet test --filter "TestCategory=Unit"

# 生成覆盖率报告
dotnet test --collect:"XPlat Code Coverage"
```

### 集成测试

```bash
# 运行集成测试
dotnet test --filter "TestCategory=Integration"
```

## 📊 日志和监控

### 日志配置

```csharp
Log.Logger = new LoggerConfiguration()
    .WriteTo.File("logs/app-.log", rollingInterval: RollingInterval.Day)
    .WriteTo.Console()
    .CreateLogger();
```

### 日志使用

```csharp
// 记录信息
Log.Information("User {UserId} logged in successfully", userId);

// 记录错误
Log.Error(ex, "Failed to activate license for user {UserId}", userId);

// 记录性能
using (Log.Logger.BeginTimedOperation("Database operation"))
{
    // 数据库操作
}
```

## 🔄 自动更新

### 检查更新

```csharp
var updateInfo = await updateService.CheckForUpdatesAsync();
if (updateInfo != null)
{
    // 发现新版本
    var (success, filePath) = await updateService.DownloadUpdateAsync(updateInfo);
    if (success)
    {
        await updateService.InstallUpdateAsync(filePath);
    }
}
```

## 🤝 贡献指南

### 开发流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范

- 使用 C# 编码规范
- 添加 XML 文档注释
- 编写单元测试
- 遵循 MVVM 模式

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

- **文档**: [项目文档](docs/)
- **问题反馈**: [GitHub Issues](https://github.com/your-repo/issues)
- **讨论**: [GitHub Discussions](https://github.com/your-repo/discussions)
- **邮箱**: <EMAIL>

## 🙏 致谢

感谢以下开源项目：

- [MaterialDesignInXamlToolkit](https://github.com/MaterialDesignInXAML/MaterialDesignInXamlToolkit)
- [ModernWpfUI](https://github.com/Kinnara/ModernWpf)
- [Serilog](https://github.com/serilog/serilog)
- [Newtonsoft.Json](https://github.com/JamesNK/Newtonsoft.Json)

## 📈 版本历史

### v2.0.0 (2024-12-19)
- ✨ 全新的 WPF 界面
- 🔐 完整的许可证系统
- 🛡️ 一机一码防盗版
- 🌐 多语言支持
- 🔄 自动更新功能

### v1.0.0 (2024-12-18)
- 🎉 初始版本发布
- 📋 基础的 Cursor 重置功能
- 💻 控制台界面

---

**尚书_CAR_cursor重置系统** - 专业的 Cursor 编辑器重置工具 🚀
