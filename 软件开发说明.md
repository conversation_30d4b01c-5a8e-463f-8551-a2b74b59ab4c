# Cursor Reset Tool Pro - 软件开发说明

## 📋 项目概述

### 项目名称
Cursor Reset Tool Pro - 带网络注册验证的 Cursor 重置工具

### 项目描述
这是一个基于 C# .NET 8.0 开发的系统工具，主要功能是重置 Cursor 编辑器的相关数据，同时集成了完整的软件授权和许可证管理系统。

### 开发目标
- 学习和演示软件授权系统的实现
- 展示文件系统和注册表操作技术
- 实现网络验证和加密存储
- 提供完整的用户体验

## 🏗️ 系统架构

### 技术栈
- **开发语言**: C# 12.0
- **运行时**: .NET 8.0
- **目标平台**: Windows (x86/x64)
- **UI框架**: Console Application
- **加密库**: System.Security.Cryptography
- **网络库**: System.Net.Http
- **序列化**: System.Text.Json

### 架构模式
采用分层架构设计：

```
┌─────────────────────────────────────┐
│           Presentation Layer        │
│  (Program.cs, UI/LicenseManager)   │
├─────────────────────────────────────┤
│           Business Layer            │
│     (Services/各种Service类)        │
├─────────────────────────────────────┤
│            Data Layer               │
│  (Models/数据模型, 本地存储)        │
├─────────────────────────────────────┤
│         Infrastructure Layer        │
│  (文件系统, 注册表, 网络通信)       │
└─────────────────────────────────────┘
```

## 📁 项目结构详解

### 核心模块

#### 1. Models/ - 数据模型层
```csharp
// LicenseInfo.cs - 许可证信息模型
public class LicenseInfo
{
    public string LicenseKey { get; set; }      // 许可证密钥
    public string UserEmail { get; set; }       // 用户邮箱
    public DateTime ExpiryDate { get; set; }    // 到期日期
    public LicenseType Type { get; set; }       // 许可证类型
    public string MachineId { get; set; }       // 机器ID
    public bool IsActivated { get; set; }       // 激活状态
    // ... 其他属性
}
```

#### 2. Services/ - 业务逻辑层

**CryptographyService.cs** - 加密服务
- 硬件指纹生成
- AES 加密/解密
- 数字签名验证
- 许可证密钥生成

**LicenseService.cs** - 许可证管理
- 许可证验证
- 本地存储管理
- 试用期管理
- 注册表操作

**ActivationService.cs** - 激活服务
- 网络激活
- 离线激活
- 服务器通信
- 错误处理

#### 3. UI/ - 用户界面层

**LicenseManager.cs** - 许可证管理界面
- 激活流程
- 状态显示
- 用户交互
- 错误提示

#### 4. Server/ - 服务器模拟

**MockLicenseServer.cs** - 模拟许可证服务器
- 激活请求处理
- 许可证验证
- 测试数据管理

### 核心功能模块

#### 1. 数据扫描 (CursorDataScanner.cs)
```csharp
public class CursorDataScanner
{
    public void ScanAll()
    {
        ScanFileSystem();    // 扫描文件系统
        ScanRegistry();      // 扫描注册表
        ShowResults();       // 显示结果
    }
}
```

#### 2. 数据备份 (CursorBackup.cs)
```csharp
public class CursorBackup
{
    public void CreateBackup()
    {
        // 创建ZIP压缩备份
        // 备份文件和目录
        // 导出注册表
    }
}
```

#### 3. 数据清理 (CursorDataCleaner.cs)
```csharp
public class CursorDataCleaner
{
    public void CleanAll()
    {
        CloseProcesses();     // 关闭进程
        CleanFiles();         // 清理文件
        CleanRegistry();      // 清理注册表
        CleanAdditionalItems(); // 清理其他项
    }
}
```

## 🔐 安全机制设计

### 1. 硬件指纹生成
```csharp
public static string GenerateMachineId()
{
    var machineInfo = new StringBuilder();
    machineInfo.Append(GetCpuId());        // CPU序列号
    machineInfo.Append(GetMotherboardId()); // 主板序列号
    machineInfo.Append(GetMacAddress());    // MAC地址
    machineInfo.Append(GetDiskId());        // 硬盘序列号
    
    // 生成SHA256哈希
    using var sha256 = SHA256.Create();
    var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineInfo.ToString()));
    return Convert.ToBase64String(hash).Substring(0, 32);
}
```

### 2. 加密存储
```csharp
public static string EncryptString(string plainText, string key)
{
    using var aes = Aes.Create();
    aes.Key = SHA256.HashData(Encoding.UTF8.GetBytes(key))[..32];
    aes.IV = new byte[16]; // 简化版本
    
    using var encryptor = aes.CreateEncryptor();
    var plainBytes = Encoding.UTF8.GetBytes(plainText);
    var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
    
    return Convert.ToBase64String(encryptedBytes);
}
```

### 3. 数字签名验证
```csharp
public static bool VerifySignature(string data, string signature, string publicKey)
{
    var expectedSignature = GenerateSignature(data, publicKey);
    return expectedSignature == signature;
}
```

## 🌐 网络通信设计

### 激活请求流程
```
客户端                    服务器
   │                        │
   ├─── ActivationRequest ──→│
   │    - LicenseKey        │
   │    - MachineId         │
   │    - UserEmail         │
   │    - ProductVersion    │
   │                        │
   │←── ActivationResponse ─┤
        - Success           │
        - License           │
        - ErrorMessage      │
```

### 数据传输格式
```json
// 激活请求
{
    "LicenseKey": "DEMO-1234-5678-9ABC",
    "MachineId": "ABC123DEF456GHI789JKL012",
    "UserEmail": "<EMAIL>",
    "ProductVersion": "1.0.0",
    "OSInfo": "Windows 11 (True)"
}

// 激活响应
{
    "Success": true,
    "Message": "许可证激活成功",
    "License": {
        "LicenseKey": "DEMO-1234-5678-9ABC",
        "UserEmail": "<EMAIL>",
        "ExpiryDate": "2025-01-15T00:00:00",
        "Type": 2,
        "IsActivated": true,
        "Signature": "..."
    }
}
```

## 💾 数据存储设计

### 1. 许可证存储
- **注册表位置**: `HKEY_CURRENT_USER\SOFTWARE\CursorResetTool`
- **文件位置**: `%APPDATA%\CursorResetTool\license.dat`
- **存储格式**: AES加密的JSON数据

### 2. 试用期存储
- **文件位置**: `%APPDATA%\CursorResetTool\trial.dat`
- **文件属性**: 隐藏 + 系统文件
- **存储格式**: AES加密的JSON数据

### 3. 备份存储
- **默认位置**: `桌面\CursorBackup\`
- **文件格式**: ZIP压缩包
- **命名规则**: `CursorBackup_YYYYMMDD_HHMMSS.zip`

## 🔄 开发流程

### 1. 环境准备
```bash
# 安装 .NET 8.0 SDK
winget install Microsoft.DotNet.SDK.8

# 克隆项目
git clone <repository-url>
cd CursorReset

# 恢复依赖
dotnet restore
```

### 2. 开发调试
```bash
# 编译项目
dotnet build

# 运行调试
dotnet run

# 运行测试
dotnet test
```

### 3. 发布部署
```bash
# 发布单文件版本
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true

# 或使用构建脚本
build.bat
```

## 🧪 测试策略

### 1. 单元测试
- 加密/解密功能测试
- 硬件指纹生成测试
- 许可证验证逻辑测试

### 2. 集成测试
- 激活流程端到端测试
- 文件操作集成测试
- 注册表操作集成测试

### 3. 用户验收测试
- 完整的用户操作流程
- 错误场景处理
- 性能和稳定性测试

## 📊 性能优化

### 1. 启动优化
- 延迟加载非关键组件
- 异步初始化
- 缓存常用数据

### 2. 内存优化
- 及时释放资源
- 使用 using 语句
- 避免内存泄漏

### 3. 网络优化
- 连接超时设置
- 重试机制
- 离线备用方案

## 🔧 配置管理

### 应用程序配置
```csharp
// 服务器地址配置
private static readonly string ActivationServerUrl = "https://api.cursorreset.com/activate";
private static readonly string ValidationServerUrl = "https://api.cursorreset.com/validate";

// 加密密钥配置
private static readonly string EncryptionKey = "CursorResetTool2024SecretKey";

// 试用期配置
public int TrialDays { get; set; } = 7;
```

### 构建配置
```xml
<PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <PublishReadyToRun>true</PublishReadyToRun>
</PropertyGroup>
```

## 🚀 部署方案

### 1. 开发环境
- Visual Studio 2022 或 VS Code
- .NET 8.0 SDK
- Windows 10/11

### 2. 生产环境
- 单文件可执行程序
- 无需安装 .NET 运行时
- 管理员权限运行

### 3. 分发方式
- 直接分发 EXE 文件
- 制作 MSI 安装包
- 在线下载更新

## 📝 开发规范

### 1. 代码规范
- 遵循 C# 编码规范
- 使用有意义的变量名
- 添加必要的注释
- 异常处理完善

### 2. 版本控制
- 使用 Git 进行版本控制
- 遵循 GitFlow 工作流
- 编写清晰的提交信息

### 3. 文档维护
- 保持文档与代码同步
- 记录重要的设计决策
- 提供使用示例

## 🔍 故障排除

### 常见问题
1. **激活失败** - 检查网络连接和许可证有效性
2. **权限不足** - 确保以管理员身份运行
3. **文件占用** - 关闭相关进程后重试
4. **加密错误** - 检查密钥配置

### 调试技巧
- 启用详细日志记录
- 使用调试器断点调试
- 检查事件日志
- 分析网络请求

## 📈 未来扩展

### 功能扩展
- 支持更多编辑器
- 云端备份功能
- 自动更新机制
- 多语言支持

### 技术升级
- 迁移到 .NET 9.0
- 使用现代加密算法
- 实现微服务架构
- 添加遥测功能

## 🔬 技术实现细节

### 1. 硬件指纹算法

硬件指纹是软件授权系统的核心，用于将许可证绑定到特定设备：

```csharp
// 获取CPU信息
private static string GetCpuId()
{
    try
    {
        using var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor");
        foreach (var obj in searcher.Get())
        {
            return obj["ProcessorId"]?.ToString() ?? "";
        }
    }
    catch { }
    return Environment.ProcessorCount.ToString();
}

// 获取主板信息
private static string GetMotherboardId()
{
    try
    {
        using var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard");
        foreach (var obj in searcher.Get())
        {
            return obj["SerialNumber"]?.ToString() ?? "";
        }
    }
    catch { }
    return "UNKNOWN";
}
```

### 2. 加密算法实现

使用 AES-256 加密算法保护敏感数据：

```csharp
public static string EncryptString(string plainText, string key)
{
    try
    {
        using var aes = Aes.Create();
        // 使用SHA256生成32字节密钥
        aes.Key = SHA256.HashData(Encoding.UTF8.GetBytes(key))[..32];
        aes.IV = new byte[16]; // 零向量（生产环境应使用随机IV）

        using var encryptor = aes.CreateEncryptor();
        var plainBytes = Encoding.UTF8.GetBytes(plainText);
        var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);

        return Convert.ToBase64String(encryptedBytes);
    }
    catch
    {
        return plainText; // 加密失败时返回原文
    }
}
```

### 3. 许可证验证流程

```csharp
public static bool IsLicenseValid()
{
    try
    {
        var license = LoadLicense();
        if (license == null) return false;

        // 1. 检查过期时间
        if (DateTime.Now > license.ExpiryDate) return false;

        // 2. 验证机器绑定
        var currentMachineId = CryptographyService.GenerateMachineId();
        if (license.MachineId != currentMachineId) return false;

        // 3. 验证数字签名
        var dataToVerify = $"{license.LicenseKey}{license.UserEmail}{license.ExpiryDate:yyyy-MM-dd}{license.MachineId}";
        if (!CryptographyService.VerifySignature(dataToVerify, license.Signature, "PublicKey"))
        {
            return false;
        }

        return license.IsActivated;
    }
    catch
    {
        return false;
    }
}
```

## 📋 API 设计规范

### 激活服务 API

#### 1. 激活请求
```http
POST /api/v1/activate
Content-Type: application/json
Authorization: Bearer <api-key>

{
    "licenseKey": "DEMO-1234-5678-9ABC",
    "machineId": "ABC123DEF456GHI789JKL012",
    "userEmail": "<EMAIL>",
    "productVersion": "1.0.0",
    "osInfo": "Windows 11 (x64)"
}
```

#### 2. 激活响应
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
    "success": true,
    "message": "License activated successfully",
    "license": {
        "licenseKey": "DEMO-1234-5678-9ABC",
        "userEmail": "<EMAIL>",
        "userName": "Demo User",
        "issueDate": "2024-01-01T00:00:00Z",
        "expiryDate": "2025-01-01T00:00:00Z",
        "type": 2,
        "machineId": "ABC123DEF456GHI789JKL012",
        "isActivated": true,
        "maxActivations": 3,
        "currentActivations": 1,
        "productVersion": "1.0.0",
        "signature": "..."
    }
}
```

#### 3. 错误响应
```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
    "success": false,
    "message": "Invalid license key",
    "errorCode": "INVALID_KEY",
    "details": "The provided license key is not valid or has expired"
}
```

## 🗄️ 数据库设计

### 许可证表 (Licenses)
```sql
CREATE TABLE Licenses (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    LicenseKey NVARCHAR(50) NOT NULL UNIQUE,
    UserEmail NVARCHAR(255) NOT NULL,
    UserName NVARCHAR(100),
    IssueDate DATETIME2 NOT NULL,
    ExpiryDate DATETIME2 NOT NULL,
    LicenseType INT NOT NULL, -- 0=Trial, 1=Personal, 2=Professional, 3=Enterprise
    MaxActivations INT NOT NULL DEFAULT 1,
    CurrentActivations INT NOT NULL DEFAULT 0,
    ProductVersion NVARCHAR(20),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);
```

### 激活记录表 (Activations)
```sql
CREATE TABLE Activations (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    LicenseId UNIQUEIDENTIFIER NOT NULL,
    MachineId NVARCHAR(50) NOT NULL,
    ActivatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastValidatedAt DATETIME2,
    OSInfo NVARCHAR(200),
    IPAddress NVARCHAR(45),
    IsActive BIT NOT NULL DEFAULT 1,

    FOREIGN KEY (LicenseId) REFERENCES Licenses(Id)
);
```

## 🔒 安全最佳实践

### 1. 密钥管理
```csharp
// 生产环境密钥管理
public class KeyManager
{
    // 从安全存储获取密钥
    public static string GetEncryptionKey()
    {
        // 从 Azure Key Vault 或其他安全存储获取
        return Environment.GetEnvironmentVariable("ENCRYPTION_KEY")
               ?? throw new InvalidOperationException("Encryption key not found");
    }

    // 密钥轮换
    public static void RotateKeys()
    {
        // 实现密钥轮换逻辑
    }
}
```

### 2. 输入验证
```csharp
public static bool ValidateLicenseKey(string licenseKey)
{
    if (string.IsNullOrWhiteSpace(licenseKey)) return false;

    // 检查格式: XXXX-XXXX-XXXX-XXXX
    var pattern = @"^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$";
    return Regex.IsMatch(licenseKey, pattern);
}

public static bool ValidateEmail(string email)
{
    if (string.IsNullOrWhiteSpace(email)) return false;

    try
    {
        var addr = new MailAddress(email);
        return addr.Address == email;
    }
    catch
    {
        return false;
    }
}
```

### 3. 错误处理
```csharp
public class LicenseException : Exception
{
    public string ErrorCode { get; }

    public LicenseException(string errorCode, string message) : base(message)
    {
        ErrorCode = errorCode;
    }
}

// 使用示例
try
{
    var result = ActivateLicense(licenseKey, email);
}
catch (LicenseException ex)
{
    Logger.LogError($"License error: {ex.ErrorCode} - {ex.Message}");
    // 向用户显示友好的错误信息
}
```

## 📊 监控和日志

### 1. 日志记录
```csharp
public static class Logger
{
    private static readonly string LogPath = Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
        "CursorResetTool", "logs");

    public static void LogInfo(string message)
    {
        WriteLog("INFO", message);
    }

    public static void LogError(string message, Exception ex = null)
    {
        var fullMessage = ex != null ? $"{message}: {ex}" : message;
        WriteLog("ERROR", fullMessage);
    }

    private static void WriteLog(string level, string message)
    {
        try
        {
            Directory.CreateDirectory(LogPath);
            var logFile = Path.Combine(LogPath, $"app_{DateTime.Now:yyyyMMdd}.log");
            var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{level}] {message}{Environment.NewLine}";
            File.AppendAllText(logFile, logEntry);
        }
        catch
        {
            // 忽略日志写入错误
        }
    }
}
```

### 2. 性能监控
```csharp
public class PerformanceMonitor
{
    private static readonly Dictionary<string, Stopwatch> Timers = new();

    public static void StartTimer(string operation)
    {
        Timers[operation] = Stopwatch.StartNew();
    }

    public static void StopTimer(string operation)
    {
        if (Timers.TryGetValue(operation, out var timer))
        {
            timer.Stop();
            Logger.LogInfo($"Operation '{operation}' took {timer.ElapsedMilliseconds}ms");
            Timers.Remove(operation);
        }
    }
}
```

## 🧪 测试框架

### 1. 单元测试示例
```csharp
[TestClass]
public class CryptographyServiceTests
{
    [TestMethod]
    public void GenerateMachineId_ShouldReturnConsistentId()
    {
        // Arrange & Act
        var id1 = CryptographyService.GenerateMachineId();
        var id2 = CryptographyService.GenerateMachineId();

        // Assert
        Assert.AreEqual(id1, id2);
        Assert.AreEqual(32, id1.Length);
    }

    [TestMethod]
    public void EncryptDecrypt_ShouldReturnOriginalText()
    {
        // Arrange
        var originalText = "Test license data";
        var key = "TestKey123";

        // Act
        var encrypted = CryptographyService.EncryptString(originalText, key);
        var decrypted = CryptographyService.DecryptString(encrypted, key);

        // Assert
        Assert.AreEqual(originalText, decrypted);
    }
}
```

### 2. 集成测试示例
```csharp
[TestClass]
public class LicenseActivationTests
{
    [TestMethod]
    public async Task ActivateLicense_WithValidKey_ShouldSucceed()
    {
        // Arrange
        var licenseKey = "TEST-1234-5678-9ABC";
        var email = "<EMAIL>";

        // Act
        var result = await ActivationService.ActivateLicenseAsync(licenseKey, email);

        // Assert
        Assert.IsTrue(result.Success);
        Assert.IsNotNull(result.License);
        Assert.AreEqual(licenseKey, result.License.LicenseKey);
    }
}
```

---

**这个扩展的开发说明文档提供了更深入的技术实现细节，包括算法实现、API设计、数据库结构、安全实践和测试策略，为开发团队提供了完整的技术指导。**
