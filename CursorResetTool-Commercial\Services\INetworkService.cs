using CursorResetTool.Commercial.Models;
using System.Net.Http;
using System.Text;

namespace CursorResetTool.Commercial.Services
{
    /// <summary>
    /// 网络服务接口
    /// </summary>
    public interface INetworkService
    {
        /// <summary>
        /// 用户注册
        /// </summary>
        Task<(bool success, string message)> RegisterUserAsync(User user);

        /// <summary>
        /// 用户登录
        /// </summary>
        Task<(bool success, User? user, string message)> LoginUserAsync(string usernameOrEmail, string password);

        /// <summary>
        /// 更新用户信息
        /// </summary>
        Task<(bool success, string message)> UpdateUserAsync(User user);

        /// <summary>
        /// 检查用户名是否存在
        /// </summary>
        Task<bool> CheckUsernameExistsAsync(string username);

        /// <summary>
        /// 检查邮箱是否存在
        /// </summary>
        Task<bool> CheckEmailExistsAsync(string email);

        /// <summary>
        /// 重置密码
        /// </summary>
        Task<(bool success, string message)> ResetPasswordAsync(string email);

        /// <summary>
        /// 验证重置密码令牌
        /// </summary>
        Task<(bool success, string message)> ValidateResetTokenAsync(string email, string token, string newPassword);

        /// <summary>
        /// 激活账户
        /// </summary>
        Task<(bool success, string message)> ActivateAccountAsync(string email, string activationCode);

        /// <summary>
        /// 激活许可证
        /// </summary>
        Task<(bool success, License? license, string message)> ActivateLicenseAsync(string licenseKey, string userEmail, string machineCode, string hardwareFingerprint);

        /// <summary>
        /// 验证许可证
        /// </summary>
        Task<(bool isValid, License? license, string message)> ValidateLicenseAsync(string licenseKey);

        /// <summary>
        /// 续费许可证
        /// </summary>
        Task<(bool success, License? license, string message)> RenewLicenseAsync(string licenseKey);

        /// <summary>
        /// 撤销许可证
        /// </summary>
        Task<(bool success, string message)> RevokeLicenseAsync(string licenseKey);

        /// <summary>
        /// 获取许可证详细信息
        /// </summary>
        Task<License?> GetLicenseDetailsAsync(string licenseKey);

        /// <summary>
        /// 检查更新
        /// </summary>
        Task<(bool hasUpdate, string version, string downloadUrl, string releaseNotes)> CheckForUpdatesAsync();

        /// <summary>
        /// 下载更新
        /// </summary>
        Task<(bool success, string filePath)> DownloadUpdateAsync(string downloadUrl, IProgress<int>? progress = null);

        /// <summary>
        /// 发送使用统计
        /// </summary>
        Task<bool> SendUsageStatisticsAsync(Dictionary<string, object> statistics);
    }

    /// <summary>
    /// 网络服务实现
    /// </summary>
    public class NetworkService : INetworkService
    {
        private readonly HttpClient _httpClient;
        private readonly ISecurityService _securityService;
        private readonly string _baseUrl;

        public NetworkService(ISecurityService securityService)
        {
            _securityService = securityService;
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
            
            // 配置基础URL - 实际应用中应该从配置文件读取
            _baseUrl = "https://api.shangshu-car.com/v1";
            
            // 设置默认请求头
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "尚书CAR-CursorResetTool/2.0");
            _httpClient.DefaultRequestHeaders.Add("X-Client-Version", "2.0.0");
        }

        public async Task<(bool success, string message)> RegisterUserAsync(User user)
        {
            try
            {
                var requestData = new
                {
                    username = user.Username,
                    email = user.Email,
                    passwordHash = user.PasswordHash,
                    salt = user.Salt,
                    membershipLevel = user.MembershipLevel.ToString(),
                    registerTime = user.RegisterTime.ToString("yyyy-MM-ddTHH:mm:ssZ")
                };

                var response = await PostAsync("/users/register", requestData);
                if (response.success)
                {
                    return (true, "注册成功");
                }
                return (false, response.message ?? "注册失败");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Network user registration failed");
                return (false, "网络连接失败");
            }
        }

        public async Task<(bool success, User? user, string message)> LoginUserAsync(string usernameOrEmail, string password)
        {
            try
            {
                var requestData = new
                {
                    usernameOrEmail,
                    password
                };

                var response = await PostAsync("/users/login", requestData);
                if (response.success && response.data != null)
                {
                    var userData = response.data.ToString();
                    var user = Newtonsoft.Json.JsonConvert.DeserializeObject<User>(userData);
                    return (true, user, "登录成功");
                }
                return (false, null, response.message ?? "登录失败");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Network user login failed");
                return (false, null, "网络连接失败");
            }
        }

        public async Task<(bool success, string message)> UpdateUserAsync(User user)
        {
            try
            {
                var requestData = new
                {
                    userId = user.UserId,
                    username = user.Username,
                    email = user.Email,
                    membershipLevel = user.MembershipLevel.ToString(),
                    settings = user.Settings
                };

                var response = await PostAsync("/users/update", requestData);
                return (response.success, response.message ?? "更新失败");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Network user update failed");
                return (false, "网络连接失败");
            }
        }

        public async Task<bool> CheckUsernameExistsAsync(string username)
        {
            try
            {
                var response = await GetAsync($"/users/check-username?username={Uri.EscapeDataString(username)}");
                return response.success && response.data?.ToString() == "true";
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> CheckEmailExistsAsync(string email)
        {
            try
            {
                var response = await GetAsync($"/users/check-email?email={Uri.EscapeDataString(email)}");
                return response.success && response.data?.ToString() == "true";
            }
            catch
            {
                return false;
            }
        }

        public async Task<(bool success, string message)> ResetPasswordAsync(string email)
        {
            try
            {
                var requestData = new { email };
                var response = await PostAsync("/users/reset-password", requestData);
                return (response.success, response.message ?? "重置失败");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Network password reset failed");
                return (false, "网络连接失败");
            }
        }

        public async Task<(bool success, string message)> ValidateResetTokenAsync(string email, string token, string newPassword)
        {
            try
            {
                var requestData = new { email, token, newPassword };
                var response = await PostAsync("/users/validate-reset-token", requestData);
                return (response.success, response.message ?? "验证失败");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Network reset token validation failed");
                return (false, "网络连接失败");
            }
        }

        public async Task<(bool success, string message)> ActivateAccountAsync(string email, string activationCode)
        {
            try
            {
                var requestData = new { email, activationCode };
                var response = await PostAsync("/users/activate", requestData);
                return (response.success, response.message ?? "激活失败");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Network account activation failed");
                return (false, "网络连接失败");
            }
        }

        public async Task<(bool success, License? license, string message)> ActivateLicenseAsync(string licenseKey, string userEmail, string machineCode, string hardwareFingerprint)
        {
            try
            {
                var requestData = new
                {
                    licenseKey,
                    userEmail,
                    machineCode,
                    hardwareFingerprint,
                    activationTime = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ")
                };

                var response = await PostAsync("/licenses/activate", requestData);
                if (response.success && response.data != null)
                {
                    var licenseData = response.data.ToString();
                    var license = Newtonsoft.Json.JsonConvert.DeserializeObject<License>(licenseData);
                    return (true, license, "许可证激活成功");
                }
                return (false, null, response.message ?? "激活失败");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Network license activation failed");
                return (false, null, "网络连接失败");
            }
        }

        public async Task<(bool isValid, License? license, string message)> ValidateLicenseAsync(string licenseKey)
        {
            try
            {
                var response = await GetAsync($"/licenses/validate?licenseKey={Uri.EscapeDataString(licenseKey)}");
                if (response.success && response.data != null)
                {
                    var licenseData = response.data.ToString();
                    var license = Newtonsoft.Json.JsonConvert.DeserializeObject<License>(licenseData);
                    return (true, license, "许可证有效");
                }
                return (false, null, response.message ?? "验证失败");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Network license validation failed");
                return (false, null, "网络连接失败");
            }
        }

        public async Task<(bool success, License? license, string message)> RenewLicenseAsync(string licenseKey)
        {
            try
            {
                var requestData = new { licenseKey };
                var response = await PostAsync("/licenses/renew", requestData);
                if (response.success && response.data != null)
                {
                    var licenseData = response.data.ToString();
                    var license = Newtonsoft.Json.JsonConvert.DeserializeObject<License>(licenseData);
                    return (true, license, "续费成功");
                }
                return (false, null, response.message ?? "续费失败");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Network license renewal failed");
                return (false, null, "网络连接失败");
            }
        }

        public async Task<(bool success, string message)> RevokeLicenseAsync(string licenseKey)
        {
            try
            {
                var requestData = new { licenseKey };
                var response = await PostAsync("/licenses/revoke", requestData);
                return (response.success, response.message ?? "撤销失败");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Network license revocation failed");
                return (false, "网络连接失败");
            }
        }

        public async Task<License?> GetLicenseDetailsAsync(string licenseKey)
        {
            try
            {
                var response = await GetAsync($"/licenses/details?licenseKey={Uri.EscapeDataString(licenseKey)}");
                if (response.success && response.data != null)
                {
                    var licenseData = response.data.ToString();
                    return Newtonsoft.Json.JsonConvert.DeserializeObject<License>(licenseData);
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to get license details");
            }
            return null;
        }

        public async Task<(bool hasUpdate, string version, string downloadUrl, string releaseNotes)> CheckForUpdatesAsync()
        {
            try
            {
                var currentVersion = "2.0.0";
                var response = await GetAsync($"/updates/check?currentVersion={currentVersion}");
                if (response.success && response.data != null)
                {
                    dynamic updateInfo = response.data;
                    return (
                        updateInfo.hasUpdate ?? false,
                        updateInfo.version?.ToString() ?? "",
                        updateInfo.downloadUrl?.ToString() ?? "",
                        updateInfo.releaseNotes?.ToString() ?? ""
                    );
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to check for updates");
            }
            return (false, "", "", "");
        }

        public async Task<(bool success, string filePath)> DownloadUpdateAsync(string downloadUrl, IProgress<int>? progress = null)
        {
            try
            {
                var fileName = Path.GetFileName(new Uri(downloadUrl).LocalPath);
                var tempPath = Path.Combine(Path.GetTempPath(), fileName);

                using var response = await _httpClient.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead);
                response.EnsureSuccessStatusCode();

                var totalBytes = response.Content.Headers.ContentLength ?? 0;
                var downloadedBytes = 0L;

                using var contentStream = await response.Content.ReadAsStreamAsync();
                using var fileStream = new FileStream(tempPath, FileMode.Create, FileAccess.Write, FileShare.None);

                var buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    await fileStream.WriteAsync(buffer, 0, bytesRead);
                    downloadedBytes += bytesRead;

                    if (totalBytes > 0)
                    {
                        var progressPercentage = (int)((downloadedBytes * 100) / totalBytes);
                        progress?.Report(progressPercentage);
                    }
                }

                return (true, tempPath);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to download update");
                return (false, "");
            }
        }

        public async Task<bool> SendUsageStatisticsAsync(Dictionary<string, object> statistics)
        {
            try
            {
                var response = await PostAsync("/statistics/usage", statistics);
                return response.success;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to send usage statistics");
                return false;
            }
        }

        private async Task<(bool success, string? message, object? data)> GetAsync(string endpoint)
        {
            try
            {
                var response = await _httpClient.GetAsync(_baseUrl + endpoint);
                var content = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(content);
                    return (true, result?.message?.ToString(), result?.data);
                }
                else
                {
                    var error = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(content);
                    return (false, error?.message?.ToString() ?? "请求失败", null);
                }
            }
            catch (HttpRequestException)
            {
                return (false, "网络连接失败", null);
            }
            catch (TaskCanceledException)
            {
                return (false, "请求超时", null);
            }
        }

        private async Task<(bool success, string? message, object? data)> PostAsync(string endpoint, object data)
        {
            try
            {
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(_baseUrl + endpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(responseContent);
                    return (true, result?.message?.ToString(), result?.data);
                }
                else
                {
                    var error = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(responseContent);
                    return (false, error?.message?.ToString() ?? "请求失败", null);
                }
            }
            catch (HttpRequestException)
            {
                return (false, "网络连接失败", null);
            }
            catch (TaskCanceledException)
            {
                return (false, "请求超时", null);
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
