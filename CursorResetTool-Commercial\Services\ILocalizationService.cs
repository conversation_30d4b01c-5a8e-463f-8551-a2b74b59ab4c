using System.Globalization;
using System.Resources;

namespace CursorResetTool.Commercial.Services
{
    /// <summary>
    /// 本地化服务接口
    /// </summary>
    public interface ILocalizationService
    {
        /// <summary>
        /// 获取当前语言
        /// </summary>
        string CurrentLanguage { get; }

        /// <summary>
        /// 设置语言
        /// </summary>
        void SetLanguage(string languageCode);

        /// <summary>
        /// 获取本地化字符串
        /// </summary>
        string GetString(string key);

        /// <summary>
        /// 获取本地化字符串（带参数）
        /// </summary>
        string GetString(string key, params object[] args);

        /// <summary>
        /// 获取支持的语言列表
        /// </summary>
        List<LanguageInfo> GetSupportedLanguages();

        /// <summary>
        /// 检查是否支持指定语言
        /// </summary>
        bool IsLanguageSupported(string languageCode);
    }

    /// <summary>
    /// 语言信息
    /// </summary>
    public class LanguageInfo
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string NativeName { get; set; } = string.Empty;
        public string Flag { get; set; } = string.Empty;
    }

    /// <summary>
    /// 本地化服务实现
    /// </summary>
    public class LocalizationService : ILocalizationService
    {
        private readonly Dictionary<string, Dictionary<string, string>> _translations;
        private string _currentLanguage;

        public string CurrentLanguage => _currentLanguage;

        public LocalizationService()
        {
            _currentLanguage = "zh-CN";
            _translations = InitializeTranslations();
        }

        public void SetLanguage(string languageCode)
        {
            if (IsLanguageSupported(languageCode))
            {
                _currentLanguage = languageCode;
                
                // 设置UI文化
                var culture = new CultureInfo(languageCode);
                CultureInfo.CurrentCulture = culture;
                CultureInfo.CurrentUICulture = culture;
            }
        }

        public string GetString(string key)
        {
            if (_translations.TryGetValue(_currentLanguage, out var languageDict) &&
                languageDict.TryGetValue(key, out var value))
            {
                return value;
            }

            // 回退到中文
            if (_currentLanguage != "zh-CN" &&
                _translations.TryGetValue("zh-CN", out var fallbackDict) &&
                fallbackDict.TryGetValue(key, out var fallbackValue))
            {
                return fallbackValue;
            }

            return $"[{key}]"; // 返回键名作为占位符
        }

        public string GetString(string key, params object[] args)
        {
            var format = GetString(key);
            try
            {
                return string.Format(format, args);
            }
            catch
            {
                return format;
            }
        }

        public List<LanguageInfo> GetSupportedLanguages()
        {
            return new List<LanguageInfo>
            {
                new() { Code = "zh-CN", Name = "Chinese (Simplified)", NativeName = "简体中文", Flag = "🇨🇳" },
                new() { Code = "en-US", Name = "English (United States)", NativeName = "English", Flag = "🇺🇸" },
                new() { Code = "zh-TW", Name = "Chinese (Traditional)", NativeName = "繁體中文", Flag = "🇹🇼" },
                new() { Code = "ja-JP", Name = "Japanese", NativeName = "日本語", Flag = "🇯🇵" },
                new() { Code = "ko-KR", Name = "Korean", NativeName = "한국어", Flag = "🇰🇷" }
            };
        }

        public bool IsLanguageSupported(string languageCode)
        {
            return _translations.ContainsKey(languageCode);
        }

        private Dictionary<string, Dictionary<string, string>> InitializeTranslations()
        {
            var translations = new Dictionary<string, Dictionary<string, string>>();

            // 中文简体
            translations["zh-CN"] = new Dictionary<string, string>
            {
                // 通用
                ["AppName"] = "尚书_CAR_cursor重置系统",
                ["Version"] = "版本",
                ["OK"] = "确定",
                ["Cancel"] = "取消",
                ["Yes"] = "是",
                ["No"] = "否",
                ["Close"] = "关闭",
                ["Save"] = "保存",
                ["Load"] = "加载",
                ["Delete"] = "删除",
                ["Refresh"] = "刷新",
                ["Settings"] = "设置",
                ["Help"] = "帮助",
                ["About"] = "关于",
                ["Exit"] = "退出",

                // 登录注册
                ["Login"] = "登录",
                ["Register"] = "注册",
                ["Username"] = "用户名",
                ["Email"] = "邮箱",
                ["Password"] = "密码",
                ["ConfirmPassword"] = "确认密码",
                ["RememberMe"] = "记住我",
                ["ForgotPassword"] = "忘记密码？",
                ["LoginSuccess"] = "登录成功",
                ["LoginFailed"] = "登录失败",
                ["RegisterSuccess"] = "注册成功",
                ["RegisterFailed"] = "注册失败",

                // 许可证
                ["License"] = "许可证",
                ["LicenseKey"] = "许可证密钥",
                ["ActivateLicense"] = "激活许可证",
                ["LicenseStatus"] = "许可证状态",
                ["LicenseType"] = "许可证类型",
                ["ExpiryDate"] = "到期日期",
                ["RemainingDays"] = "剩余天数",
                ["Trial"] = "试用版",
                ["Personal"] = "个人版",
                ["Professional"] = "专业版",
                ["Enterprise"] = "企业版",

                // 主要功能
                ["Scan"] = "扫描",
                ["Backup"] = "备份",
                ["Clean"] = "清理",
                ["Restore"] = "恢复",
                ["ScanCursorData"] = "扫描Cursor数据",
                ["BackupCursorData"] = "备份Cursor数据",
                ["CleanCursorData"] = "清理Cursor数据",
                ["RestoreCursorData"] = "恢复Cursor数据",

                // 状态消息
                ["Ready"] = "就绪",
                ["Processing"] = "处理中...",
                ["Completed"] = "完成",
                ["Failed"] = "失败",
                ["Warning"] = "警告",
                ["Error"] = "错误",
                ["Success"] = "成功",

                // 设置
                ["Language"] = "语言",
                ["Theme"] = "主题",
                ["AutoUpdate"] = "自动更新",
                ["AutoBackup"] = "自动备份",
                ["LogLevel"] = "日志级别",

                // 错误消息
                ["NetworkError"] = "网络连接错误",
                ["FileNotFound"] = "文件未找到",
                ["AccessDenied"] = "访问被拒绝",
                ["InvalidInput"] = "输入无效",
                ["OperationCancelled"] = "操作已取消",

                // 确认对话框
                ["ConfirmClean"] = "确定要清理Cursor数据吗？此操作不可撤销！",
                ["ConfirmDelete"] = "确定要删除此项吗？",
                ["ConfirmExit"] = "确定要退出程序吗？",

                // 进度信息
                ["Initializing"] = "正在初始化...",
                ["Scanning"] = "正在扫描...",
                ["Backing"] = "正在备份...",
                ["Cleaning"] = "正在清理...",
                ["Restoring"] = "正在恢复...",
                ["Downloading"] = "正在下载...",
                ["Installing"] = "正在安装...",

                // 文件操作
                ["SelectFile"] = "选择文件",
                ["SelectFolder"] = "选择文件夹",
                ["FileName"] = "文件名",
                ["FileSize"] = "文件大小",
                ["CreateTime"] = "创建时间",
                ["ModifyTime"] = "修改时间",

                // 系统信息
                ["SystemInfo"] = "系统信息",
                ["OperatingSystem"] = "操作系统",
                ["Architecture"] = "架构",
                ["MachineCode"] = "机器码",
                ["HardwareInfo"] = "硬件信息",

                // 更新
                ["CheckUpdate"] = "检查更新",
                ["UpdateAvailable"] = "发现新版本",
                ["UpdateNotAvailable"] = "已是最新版本",
                ["DownloadUpdate"] = "下载更新",
                ["InstallUpdate"] = "安装更新",
                ["UpdateCompleted"] = "更新完成",

                // 日志
                ["ViewLogs"] = "查看日志",
                ["ClearLogs"] = "清理日志",
                ["ExportLogs"] = "导出日志",
                ["LogLevel_Debug"] = "调试",
                ["LogLevel_Info"] = "信息",
                ["LogLevel_Warning"] = "警告",
                ["LogLevel_Error"] = "错误",
                ["LogLevel_Critical"] = "严重"
            };

            // 英文
            translations["en-US"] = new Dictionary<string, string>
            {
                // 通用
                ["AppName"] = "Shangshu CAR Cursor Reset System",
                ["Version"] = "Version",
                ["OK"] = "OK",
                ["Cancel"] = "Cancel",
                ["Yes"] = "Yes",
                ["No"] = "No",
                ["Close"] = "Close",
                ["Save"] = "Save",
                ["Load"] = "Load",
                ["Delete"] = "Delete",
                ["Refresh"] = "Refresh",
                ["Settings"] = "Settings",
                ["Help"] = "Help",
                ["About"] = "About",
                ["Exit"] = "Exit",

                // 登录注册
                ["Login"] = "Login",
                ["Register"] = "Register",
                ["Username"] = "Username",
                ["Email"] = "Email",
                ["Password"] = "Password",
                ["ConfirmPassword"] = "Confirm Password",
                ["RememberMe"] = "Remember Me",
                ["ForgotPassword"] = "Forgot Password?",
                ["LoginSuccess"] = "Login Successful",
                ["LoginFailed"] = "Login Failed",
                ["RegisterSuccess"] = "Registration Successful",
                ["RegisterFailed"] = "Registration Failed",

                // 许可证
                ["License"] = "License",
                ["LicenseKey"] = "License Key",
                ["ActivateLicense"] = "Activate License",
                ["LicenseStatus"] = "License Status",
                ["LicenseType"] = "License Type",
                ["ExpiryDate"] = "Expiry Date",
                ["RemainingDays"] = "Remaining Days",
                ["Trial"] = "Trial",
                ["Personal"] = "Personal",
                ["Professional"] = "Professional",
                ["Enterprise"] = "Enterprise",

                // 主要功能
                ["Scan"] = "Scan",
                ["Backup"] = "Backup",
                ["Clean"] = "Clean",
                ["Restore"] = "Restore",
                ["ScanCursorData"] = "Scan Cursor Data",
                ["BackupCursorData"] = "Backup Cursor Data",
                ["CleanCursorData"] = "Clean Cursor Data",
                ["RestoreCursorData"] = "Restore Cursor Data",

                // 状态消息
                ["Ready"] = "Ready",
                ["Processing"] = "Processing...",
                ["Completed"] = "Completed",
                ["Failed"] = "Failed",
                ["Warning"] = "Warning",
                ["Error"] = "Error",
                ["Success"] = "Success",

                // 设置
                ["Language"] = "Language",
                ["Theme"] = "Theme",
                ["AutoUpdate"] = "Auto Update",
                ["AutoBackup"] = "Auto Backup",
                ["LogLevel"] = "Log Level",

                // 错误消息
                ["NetworkError"] = "Network Connection Error",
                ["FileNotFound"] = "File Not Found",
                ["AccessDenied"] = "Access Denied",
                ["InvalidInput"] = "Invalid Input",
                ["OperationCancelled"] = "Operation Cancelled",

                // 确认对话框
                ["ConfirmClean"] = "Are you sure you want to clean Cursor data? This operation cannot be undone!",
                ["ConfirmDelete"] = "Are you sure you want to delete this item?",
                ["ConfirmExit"] = "Are you sure you want to exit the application?",

                // 进度信息
                ["Initializing"] = "Initializing...",
                ["Scanning"] = "Scanning...",
                ["Backing"] = "Backing up...",
                ["Cleaning"] = "Cleaning...",
                ["Restoring"] = "Restoring...",
                ["Downloading"] = "Downloading...",
                ["Installing"] = "Installing...",

                // 文件操作
                ["SelectFile"] = "Select File",
                ["SelectFolder"] = "Select Folder",
                ["FileName"] = "File Name",
                ["FileSize"] = "File Size",
                ["CreateTime"] = "Create Time",
                ["ModifyTime"] = "Modify Time",

                // 系统信息
                ["SystemInfo"] = "System Information",
                ["OperatingSystem"] = "Operating System",
                ["Architecture"] = "Architecture",
                ["MachineCode"] = "Machine Code",
                ["HardwareInfo"] = "Hardware Information",

                // 更新
                ["CheckUpdate"] = "Check for Updates",
                ["UpdateAvailable"] = "Update Available",
                ["UpdateNotAvailable"] = "No Updates Available",
                ["DownloadUpdate"] = "Download Update",
                ["InstallUpdate"] = "Install Update",
                ["UpdateCompleted"] = "Update Completed",

                // 日志
                ["ViewLogs"] = "View Logs",
                ["ClearLogs"] = "Clear Logs",
                ["ExportLogs"] = "Export Logs",
                ["LogLevel_Debug"] = "Debug",
                ["LogLevel_Info"] = "Information",
                ["LogLevel_Warning"] = "Warning",
                ["LogLevel_Error"] = "Error",
                ["LogLevel_Critical"] = "Critical"
            };

            return translations;
        }
    }
}
