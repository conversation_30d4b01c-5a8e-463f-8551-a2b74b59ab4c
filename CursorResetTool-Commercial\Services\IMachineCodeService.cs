using System.Management;
using System.Security.Cryptography;
using System.Text;

namespace CursorResetTool.Commercial.Services
{
    /// <summary>
    /// 机器码服务接口
    /// </summary>
    public interface IMachineCodeService
    {
        /// <summary>
        /// 生成机器码
        /// </summary>
        string GenerateMachineCode();

        /// <summary>
        /// 生成硬件指纹
        /// </summary>
        string GenerateHardwareFingerprint();

        /// <summary>
        /// 验证机器码
        /// </summary>
        bool ValidateMachineCode(string machineCode);

        /// <summary>
        /// 获取CPU信息
        /// </summary>
        string GetCpuInfo();

        /// <summary>
        /// 获取主板信息
        /// </summary>
        string GetMotherboardInfo();

        /// <summary>
        /// 获取硬盘信息
        /// </summary>
        string GetHardDiskInfo();

        /// <summary>
        /// 获取内存信息
        /// </summary>
        string GetMemoryInfo();

        /// <summary>
        /// 获取网卡信息
        /// </summary>
        string GetNetworkAdapterInfo();
    }

    /// <summary>
    /// 机器码服务实现
    /// </summary>
    public class MachineCodeService : IMachineCodeService
    {
        private readonly ISecurityService _securityService;

        public MachineCodeService(ISecurityService securityService)
        {
            _securityService = securityService;
        }

        public string GenerateMachineCode()
        {
            try
            {
                var components = new List<string>
                {
                    GetCpuInfo(),
                    GetMotherboardInfo(),
                    GetHardDiskInfo(),
                    GetMemoryInfo(),
                    GetNetworkAdapterInfo()
                };

                var combinedInfo = string.Join("|", components.Where(c => !string.IsNullOrEmpty(c)));
                
                using var sha256 = SHA256.Create();
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(combinedInfo));
                var machineCode = Convert.ToBase64String(hash);

                // 格式化为更友好的格式
                return FormatMachineCode(machineCode);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to generate machine code");
                return GenerateFallbackMachineCode();
            }
        }

        public string GenerateHardwareFingerprint()
        {
            try
            {
                var fingerprint = new
                {
                    CPU = GetCpuInfo(),
                    Motherboard = GetMotherboardInfo(),
                    HardDisk = GetHardDiskInfo(),
                    Memory = GetMemoryInfo(),
                    NetworkAdapter = GetNetworkAdapterInfo(),
                    Timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd")
                };

                var json = Newtonsoft.Json.JsonConvert.SerializeObject(fingerprint);
                return _securityService.EncryptAES(json, "HardwareFingerprint2024");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to generate hardware fingerprint");
                return string.Empty;
            }
        }

        public bool ValidateMachineCode(string machineCode)
        {
            try
            {
                var currentMachineCode = GenerateMachineCode();
                return string.Equals(machineCode, currentMachineCode, StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        public string GetCpuInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT ProcessorId, Name FROM Win32_Processor");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var processorId = obj["ProcessorId"]?.ToString();
                    var name = obj["Name"]?.ToString();
                    if (!string.IsNullOrEmpty(processorId))
                    {
                        return $"{processorId}_{name?.Replace(" ", "")}";
                    }
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Warning(ex, "Failed to get CPU info");
            }
            return Environment.ProcessorCount.ToString();
        }

        public string GetMotherboardInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT SerialNumber, Product FROM Win32_BaseBoard");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var serialNumber = obj["SerialNumber"]?.ToString();
                    var product = obj["Product"]?.ToString();
                    if (!string.IsNullOrEmpty(serialNumber) && serialNumber != "To be filled by O.E.M.")
                    {
                        return $"{serialNumber}_{product?.Replace(" ", "")}";
                    }
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Warning(ex, "Failed to get motherboard info");
            }
            return Environment.MachineName;
        }

        public string GetHardDiskInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT SerialNumber, Model FROM Win32_DiskDrive WHERE MediaType='Fixed hard disk media'");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var serialNumber = obj["SerialNumber"]?.ToString()?.Trim();
                    var model = obj["Model"]?.ToString();
                    if (!string.IsNullOrEmpty(serialNumber))
                    {
                        return $"{serialNumber}_{model?.Replace(" ", "")}";
                    }
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Warning(ex, "Failed to get hard disk info");
            }
            return DriveInfo.GetDrives().FirstOrDefault()?.Name ?? "C:";
        }

        public string GetMemoryInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT SerialNumber, Capacity FROM Win32_PhysicalMemory");
                var memoryInfo = new List<string>();
                foreach (ManagementObject obj in searcher.Get())
                {
                    var serialNumber = obj["SerialNumber"]?.ToString()?.Trim();
                    var capacity = obj["Capacity"]?.ToString();
                    if (!string.IsNullOrEmpty(serialNumber))
                    {
                        memoryInfo.Add($"{serialNumber}_{capacity}");
                    }
                }
                if (memoryInfo.Any())
                {
                    return string.Join("|", memoryInfo);
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Warning(ex, "Failed to get memory info");
            }
            return GC.GetTotalMemory(false).ToString();
        }

        public string GetNetworkAdapterInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT MACAddress, PNPDeviceID FROM Win32_NetworkAdapter WHERE MACAddress IS NOT NULL AND PNPDeviceID IS NOT NULL");
                var adapters = new List<string>();
                foreach (ManagementObject obj in searcher.Get())
                {
                    var macAddress = obj["MACAddress"]?.ToString();
                    var pnpDeviceId = obj["PNPDeviceID"]?.ToString();
                    if (!string.IsNullOrEmpty(macAddress) && !string.IsNullOrEmpty(pnpDeviceId))
                    {
                        adapters.Add($"{macAddress}_{pnpDeviceId}");
                    }
                }
                if (adapters.Any())
                {
                    return string.Join("|", adapters.Take(2)); // 只取前两个网卡
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Warning(ex, "Failed to get network adapter info");
            }
            return Environment.MachineName;
        }

        private string FormatMachineCode(string machineCode)
        {
            // 将机器码格式化为 XXXX-XXXX-XXXX-XXXX 格式
            var cleanCode = machineCode.Replace("=", "").Replace("+", "").Replace("/", "");
            if (cleanCode.Length >= 16)
            {
                return $"{cleanCode.Substring(0, 4)}-{cleanCode.Substring(4, 4)}-{cleanCode.Substring(8, 4)}-{cleanCode.Substring(12, 4)}";
            }
            return cleanCode;
        }

        private string GenerateFallbackMachineCode()
        {
            // 备用机器码生成方法
            var fallbackInfo = $"{Environment.MachineName}_{Environment.ProcessorCount}_{Environment.OSVersion}";
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(fallbackInfo));
            var machineCode = Convert.ToBase64String(hash);
            return FormatMachineCode(machineCode);
        }
    }
}
