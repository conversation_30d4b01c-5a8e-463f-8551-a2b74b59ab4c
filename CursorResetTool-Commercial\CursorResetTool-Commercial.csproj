<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyTitle>尚书_CAR_cursor重置系统</AssemblyTitle>
    <AssemblyDescription>Professional Cursor Reset System with Modern UI and License Management</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Copyright>Copyright © 2024 尚书科技</Copyright>
    <ApplicationIcon>Resources\icon.ico</ApplicationIcon>
    <StartupObject>CursorResetTool.Commercial.App</StartupObject>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Management" Version="8.0.0" />
    <PackageReference Include="System.Diagnostics.EventLog" Version="8.0.0" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="ModernWpfUI" Version="0.9.6" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="QRCoder" Version="1.4.3" />
    <PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
    <EmbeddedResource Include="Localization\**\*.resx" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Controls\" />
    <Folder Include="Converters\" />
    <Folder Include="Resources\Images\" />
    <Folder Include="Resources\Icons\" />
    <Folder Include="Styles\" />
    <Folder Include="Localization\" />
  </ItemGroup>

</Project>
