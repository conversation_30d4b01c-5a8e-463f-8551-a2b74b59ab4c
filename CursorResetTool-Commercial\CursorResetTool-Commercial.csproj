<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyTitle>Cursor Reset Tool Pro - Commercial Edition</AssemblyTitle>
    <AssemblyDescription>Professional Cursor Reset Tool with Modern UI and License Management</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Copyright>Copyright © 2024 Professional Software Solutions</Copyright>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <StartupObject>CursorResetTool.Commercial.App</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Management" Version="8.0.0" />
    <PackageReference Include="System.Diagnostics.EventLog" Version="8.0.0" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="ModernWpfUI" Version="0.9.6" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="QRCoder" Version="1.4.3" />
    <PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
  </ItemGroup>

</Project>
