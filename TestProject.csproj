<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyTitle>Cursor Reset Tool Test</AssemblyTitle>
    <AssemblyDescription>Test program for Cursor Reset Tool</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <StartupObject>CursorReset.Test.TestProgram</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Management" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="TestProgram.cs" />
    <Compile Include="Models\LicenseInfo.cs" />
    <Compile Include="Services\LoggingService.cs" />
    <Compile Include="Services\CryptographyService.cs" />
    <Compile Include="Services\LicenseService.cs" />
    <Compile Include="Services\ActivationService.cs" />
    <Compile Include="UI\LicenseManager.cs" />
    <Compile Include="Server\MockLicenseServer.cs" />
    <Compile Include="CursorDataScanner.cs" />
    <Compile Include="CursorBackup.cs" />
    <Compile Include="CursorDataCleaner.cs" />
  </ItemGroup>

</Project>
