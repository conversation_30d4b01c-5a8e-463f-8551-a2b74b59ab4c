@echo off
title Fixed WPF Launcher

echo ========================================
echo    Fixed WPF Application Launcher
echo ========================================
echo.

echo Checking system environment...
echo.

REM Get current directory
set CURRENT_DIR=%~dp0
echo Current directory: %CURRENT_DIR%

REM Check multiple possible locations
set PROGRAM_PATH1=%CURRENT_DIR%CursorResetTool-Demo\bin\Debug\net8.0-windows\CursorResetTool-Demo.exe
set PROGRAM_PATH2=%CURRENT_DIR%CursorResetTool-Demo\bin\Release\net8.0-windows\CursorResetTool-Demo.exe

echo Checking for program file...
echo Path 1: %PROGRAM_PATH1%
echo Path 2: %PROGRAM_PATH2%
echo.

if exist "%PROGRAM_PATH1%" (
    echo [OK] Found program file in Debug folder
    set PROGRAM_PATH=%PROGRAM_PATH1%
    goto :check_net
)

if exist "%PROGRAM_PATH2%" (
    echo [OK] Found program file in Release folder
    set PROGRAM_PATH=%PROGRAM_PATH2%
    goto :check_net
)

echo [ERROR] Program file not found in either location!
echo.
echo Trying to build the project...
echo.

REM Try to build the project
if exist "%CURRENT_DIR%CursorResetTool-Demo\CursorResetTool-Demo.csproj" (
    echo Building project...
    cd /d "%CURRENT_DIR%CursorResetTool-Demo"
    dotnet build
    cd /d "%CURRENT_DIR%"
    
    REM Check again after build
    if exist "%PROGRAM_PATH1%" (
        echo [OK] Build successful! Program file created.
        set PROGRAM_PATH=%PROGRAM_PATH1%
        goto :check_net
    ) else (
        echo [ERROR] Build failed or program file still not found.
        goto :build_error
    )
) else (
    echo [ERROR] Project file not found!
    goto :build_error
)

:build_error
echo.
echo ========================================
echo    Build Error
echo ========================================
echo.
echo Could not find or build the program.
echo.
echo Please check:
echo 1. CursorResetTool-Demo folder exists
echo 2. Project file CursorResetTool-Demo.csproj exists
echo 3. .NET SDK is installed for building
echo.
echo Manual build command:
echo   cd CursorResetTool-Demo
echo   dotnet build
echo.
pause
exit /b 1

:check_net
echo.
echo Program found: %PROGRAM_PATH%
echo.
echo Checking .NET environment...

REM Try to start the program directly first
echo Attempting to start application...
start "" "%PROGRAM_PATH%"

echo.
echo Application launch attempted!
echo.
echo If the application doesn't start, you may need to:
echo 1. Install .NET 8.0 Desktop Runtime
echo 2. Download from: https://dotnet.microsoft.com/download/dotnet/8.0
echo 3. Select "Desktop Runtime x64"
echo.

timeout /t 3 /nobreak >nul
exit /b 0
