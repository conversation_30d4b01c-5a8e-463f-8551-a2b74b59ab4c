using System.Windows;
using System.Windows.Input;
using Microsoft.Extensions.DependencyInjection;
using CursorResetTool.Commercial.Services;

namespace CursorResetTool.Commercial.Views
{
    public partial class LoginWindow : Window
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IUserService _userService;
        private readonly ILicenseService _licenseService;
        private readonly ILocalizationService _localizationService;

        public LoginWindow()
        {
            InitializeComponent();
            
            // 从App获取服务提供者
            _serviceProvider = ((App)Application.Current).Host?.Services ?? 
                throw new InvalidOperationException("Service provider not available");

            _userService = _serviceProvider.GetRequiredService<IUserService>();
            _licenseService = _serviceProvider.GetRequiredService<ILicenseService>();
            _localizationService = _serviceProvider.GetRequiredService<ILocalizationService>();

            // 设置窗口可拖拽
            this.MouseLeftButtonDown += (sender, e) => this.DragMove();

            // 设置焦点
            this.Loaded += (sender, e) => UsernameTextBox.Focus();

            // 回车键登录
            this.KeyDown += LoginWindow_KeyDown;
        }

        private void LoginWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(sender, e);
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var username = UsernameTextBox.Text.Trim();
                var password = PasswordBox.Password;

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    UpdateStatus("请输入用户名和密码", true);
                    return;
                }

                UpdateStatus("正在登录...", false);
                LoginButton.IsEnabled = false;

                // 执行登录
                var (success, user, message) = await _userService.LoginAsync(username, password);

                if (success && user != null)
                {
                    // 保存记住我设置
                    if (RememberMeCheckBox.IsChecked == true)
                    {
                        user.Settings.RememberLogin = true;
                        await _userService.SaveUserSettingsAsync(user.Settings);
                    }

                    UpdateStatus("登录成功！正在进入主界面...", false);

                    // 延迟一下显示成功消息
                    await Task.Delay(1000);

                    // 打开主窗口
                    var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
                    mainWindow.Show();

                    // 关闭登录窗口
                    this.Close();
                }
                else
                {
                    UpdateStatus($"登录失败：{message}", true);
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Login failed");
                UpdateStatus($"登录失败：{ex.Message}", true);
            }
            finally
            {
                LoginButton.IsEnabled = true;
            }
        }

        private void RegisterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var registerWindow = _serviceProvider.GetRequiredService<RegisterWindow>();
                registerWindow.Owner = this;
                registerWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to open register window");
                UpdateStatus($"打开注册窗口失败：{ex.Message}", true);
            }
        }

        private async void TrialButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("正在生成试用许可证...", false);
                TrialButton.IsEnabled = false;

                // 生成试用许可证
                var email = "<EMAIL>"; // 试用版使用默认邮箱
                var (success, license, message) = await _licenseService.GenerateTrialLicenseAsync(email);

                if (success && license != null)
                {
                    // 创建试用用户
                    var trialUser = new Models.User
                    {
                        UserId = Guid.NewGuid().ToString(),
                        Username = "试用用户",
                        Email = email,
                        MembershipLevel = Models.MembershipLevel.Trial,
                        RegisterTime = DateTime.Now,
                        IsActive = true
                    };

                    _userService.SetCurrentUser(trialUser);
                    _licenseService.SetCurrentLicense(license);

                    UpdateStatus("试用许可证生成成功！正在进入主界面...", false);
                    await Task.Delay(1000);

                    // 打开主窗口
                    var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
                    mainWindow.Show();

                    // 关闭登录窗口
                    this.Close();
                }
                else
                {
                    UpdateStatus($"试用许可证生成失败：{message}", true);
                }
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Trial license generation failed");
                UpdateStatus($"试用许可证生成失败：{ex.Message}", true);
            }
            finally
            {
                TrialButton.IsEnabled = true;
            }
        }

        private void ForgotPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var email = UsernameTextBox.Text.Trim();
                if (string.IsNullOrEmpty(email))
                {
                    UpdateStatus("请先输入邮箱地址", true);
                    return;
                }

                // 这里可以实现密码重置功能
                MessageBox.Show(
                    $"密码重置链接已发送到 {email}\n请查收邮件并按照说明重置密码。",
                    "密码重置",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Password reset failed");
                UpdateStatus($"密码重置失败：{ex.Message}", true);
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsWindow = _serviceProvider.GetRequiredService<SettingsWindow>();
                settingsWindow.Owner = this;
                settingsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to open settings window");
                UpdateStatus($"打开设置窗口失败：{ex.Message}", true);
            }
        }

        private void HelpButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var helpText = @"尚书_CAR_cursor重置系统 - 帮助

功能说明：
• 专业的Cursor编辑器重置工具
• 支持完整的数据备份和恢复
• 企业级许可证管理系统
• 一机一码防盗版保护

使用方法：
1. 注册账户或使用试用版
2. 激活许可证
3. 扫描和备份Cursor数据
4. 执行重置操作

技术支持：
邮箱：<EMAIL>
网站：https://www.shangshu-car.com

版本：2.0.0
Copyright © 2024 尚书科技";

                MessageBox.Show(helpText, "帮助", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to show help");
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void UpdateStatus(string message, bool isError)
        {
            StatusText.Text = message;
            StatusText.Foreground = isError ? 
                (System.Windows.Media.Brush)FindResource("ErrorBrush") : 
                (System.Windows.Media.Brush)FindResource("TextSecondaryBrush");
        }
    }
}
