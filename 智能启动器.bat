@echo off
title Smart WPF Launcher

echo ========================================
echo    Smart WPF Application Launcher
echo ========================================
echo.

echo Checking system environment...
echo.

REM Check if program file exists
if not exist "CursorResetTool-Demo\bin\Debug\net8.0-windows\CursorResetTool-Demo.exe" (
    echo [ERROR] Program file not found!
    echo Please build the project first: dotnet build CursorResetTool-Demo
    echo.
    pause
    exit /b 1
)

REM Try to check .NET installation
echo Checking .NET environment...
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] .NET is installed
    
    REM Check for .NET 8.0 Desktop Runtime
    dotnet --list-runtimes 2>nul | findstr "Microsoft.WindowsDesktop.App 8.0" >nul
    if %errorlevel% equ 0 (
        echo [OK] .NET 8.0 Desktop Runtime found
        echo [INFO] Starting application...
        echo.
        start "" "CursorResetTool-Demo\bin\Debug\net8.0-windows\CursorResetTool-Demo.exe"
        echo Application launched successfully!
        echo.
        timeout /t 2 /nobreak >nul
        exit /b 0
    ) else (
        echo [WARNING] .NET 8.0 Desktop Runtime not found
        goto :show_install_options
    )
) else (
    echo [WARNING] .NET not found in PATH
    goto :show_install_options
)

:show_install_options
echo.
echo ========================================
echo    .NET Installation Required
echo ========================================
echo.
echo The application requires .NET 8.0 Desktop Runtime.
echo.
echo Option 1: Install .NET 8.0 Desktop Runtime (Recommended)
echo   - Download from: https://dotnet.microsoft.com/download/dotnet/8.0
echo   - Select: Desktop Runtime x64
echo   - Size: ~50MB
echo   - Benefits: Fast startup, supports all .NET 8.0 apps
echo.
echo Option 2: Try to run anyway (may prompt for download)
echo   - Windows may automatically prompt to download .NET
echo   - Follow the system prompts to install
echo.
echo Option 3: Request portable version
echo   - Contact developer for self-contained version
echo   - No installation required, but larger file size
echo.
echo ========================================
echo.

:ask_user
set /p choice="Choose option (1=Install, 2=Try anyway, 3=Info, Q=Quit): "

if /i "%choice%"=="1" goto :install_net
if /i "%choice%"=="2" goto :try_anyway
if /i "%choice%"=="3" goto :show_info
if /i "%choice%"=="q" goto :quit
if /i "%choice%"=="quit" goto :quit

echo Invalid choice. Please enter 1, 2, 3, or Q.
goto :ask_user

:install_net
echo.
echo Opening .NET download page...
start "" "https://dotnet.microsoft.com/download/dotnet/8.0"
echo.
echo Please:
echo 1. Download "Desktop Runtime x64" from the opened page
echo 2. Install the downloaded file
echo 3. Run this launcher again
echo.
pause
exit /b 0

:try_anyway
echo.
echo Attempting to start application...
echo (Windows may prompt to download .NET)
echo.
start "" "CursorResetTool-Demo\bin\Debug\net8.0-windows\CursorResetTool-Demo.exe"
echo.
echo If the application doesn't start:
echo 1. Follow any system prompts to install .NET
echo 2. Or choose Option 1 to manually install
echo.
timeout /t 3 /nobreak >nul
exit /b 0

:show_info
echo.
echo ========================================
echo    Detailed Information
echo ========================================
echo.
echo About .NET 8.0 Desktop Runtime:
echo - Official Microsoft component
echo - Required for WPF applications
echo - Safe and secure installation
echo - Long-term support (LTS) version
echo - Compatible with Windows 10/11
echo.
echo Installation benefits:
echo - One-time setup for all .NET 8.0 apps
echo - Faster application startup
echo - Smaller application files
echo - Better performance and compatibility
echo.
echo Alternative: Portable version
echo - No installation required
echo - Larger file size (~100MB vs ~15MB)
echo - Slower first startup
echo - Self-contained with all dependencies
echo.
echo ========================================
echo.
goto :ask_user

:quit
echo.
echo Exiting launcher...
exit /b 0
