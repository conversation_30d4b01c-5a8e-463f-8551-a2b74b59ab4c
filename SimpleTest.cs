using System;
using System.IO;

namespace CursorReset.Test
{
    class SimpleTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Cursor Reset Tool Pro - 简化测试版 ===");
            Console.WriteLine("警告：此工具仅用于学习目的！");
            Console.WriteLine();

            try
            {
                // 检查管理员权限
                if (!IsRunAsAdministrator())
                {
                    Console.WriteLine("❌ 需要管理员权限运行此程序");
                    Console.WriteLine("请右键点击程序，选择'以管理员身份运行'");
                    Console.WriteLine("按任意键退出...");
                    Console.ReadKey();
                    return;
                }

                Console.WriteLine("✅ 检测到管理员权限");
                Console.WriteLine();

                // 模拟许可证检查
                Console.WriteLine("🔍 检查许可证状态...");
                Console.WriteLine("❌ 许可证无效或试用期已过期");
                Console.WriteLine();
                Console.WriteLine("请选择：");
                Console.WriteLine("1. 激活许可证");
                Console.WriteLine("2. 离线激活");
                Console.WriteLine("3. 申请试用");
                Console.WriteLine("4. 退出程序");
                Console.Write("请选择 (1-4): ");

                var choice = Console.ReadLine();
                Console.WriteLine();

                switch (choice)
                {
                    case "1":
                        TestOnlineActivation();
                        break;
                    case "2":
                        TestOfflineActivation();
                        break;
                    case "3":
                        TestTrialActivation();
                        break;
                    case "4":
                    default:
                        Console.WriteLine("程序退出。");
                        return;
                }

                // 显示主菜单
                ShowMainMenu();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"程序发生错误: {ex.Message}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }

        static bool IsRunAsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        static void TestOnlineActivation()
        {
            Console.WriteLine("=== 在线激活许可证 ===");
            Console.WriteLine();
            
            Console.Write("请输入许可证密钥: ");
            var licenseKey = Console.ReadLine();
            
            Console.Write("请输入邮箱地址: ");
            var email = Console.ReadLine();
            
            Console.WriteLine();
            Console.WriteLine("🔄 正在激活许可证...");
            Console.WriteLine("🔄 尝试连接许可证服务器...");
            
            // 模拟网络延迟
            System.Threading.Thread.Sleep(2000);
            
            // 模拟激活成功
            if (licenseKey == "DEMO-1234-5678-9ABC" || licenseKey == "TEST-ABCD-EFGH-IJKL")
            {
                Console.WriteLine("✅ 服务器激活成功");
                Console.WriteLine("✅ 许可证激活成功！");
                Console.WriteLine("许可证类型: 专业版");
                Console.WriteLine("到期日期: 2025-01-15");
            }
            else
            {
                Console.WriteLine("❌ 激活失败: 无效的许可证密钥");
                Console.WriteLine("请使用测试密钥: DEMO-1234-5678-9ABC");
            }
        }

        static void TestOfflineActivation()
        {
            Console.WriteLine("=== 离线激活许可证 ===");
            Console.WriteLine();
            
            Console.Write("请输入许可证密钥: ");
            var licenseKey = Console.ReadLine();
            
            Console.Write("请输入邮箱地址: ");
            var email = Console.ReadLine();
            
            Console.WriteLine();
            Console.WriteLine("🔄 正在离线激活许可证...");
            
            // 模拟离线激活
            System.Threading.Thread.Sleep(1000);
            
            if (licenseKey == "DEMO-1234-5678-9ABC" || licenseKey == "TEST-ABCD-EFGH-IJKL")
            {
                Console.WriteLine("✅ 离线激活成功！");
                Console.WriteLine("许可证类型: 专业版");
                Console.WriteLine("到期日期: 2025-01-15");
            }
            else
            {
                Console.WriteLine("❌ 离线激活失败: 无效的许可证密钥");
            }
        }

        static void TestTrialActivation()
        {
            Console.WriteLine("=== 申请试用许可证 ===");
            Console.WriteLine();
            
            Console.WriteLine("🔄 正在生成试用许可证...");
            System.Threading.Thread.Sleep(1000);
            
            Console.WriteLine("✅ 试用许可证生成成功！");
            Console.WriteLine("试用密钥: TRIAL-XXXX-XXXX-XXXX");
            Console.WriteLine("试用期限: 7 天");
            Console.WriteLine("💡 试用期间可以使用所有功能");
        }

        static void ShowMainMenu()
        {
            Console.WriteLine();
            Console.WriteLine("=== Cursor Reset Tool Pro ===");
            Console.WriteLine("✅ 已授权 | 用户: <EMAIL> | 类型: 专业版");
            Console.WriteLine("⏰ 许可证剩余: 365 天");
            Console.WriteLine();
            
            while (true)
            {
                Console.WriteLine("请选择操作：");
                Console.WriteLine("1. 扫描 Cursor 相关文件和注册表");
                Console.WriteLine("2. 备份 Cursor 数据");
                Console.WriteLine("3. 清理 Cursor 数据 (谨慎操作!)");
                Console.WriteLine("4. 恢复备份");
                Console.WriteLine("5. 许可证管理");
                Console.WriteLine("6. 查看日志");
                Console.WriteLine("7. 退出");
                Console.Write("请输入选项 (1-7): ");

                var choice = Console.ReadLine();
                Console.WriteLine();

                switch (choice)
                {
                    case "1":
                        TestScanFunction();
                        break;
                    case "2":
                        TestBackupFunction();
                        break;
                    case "3":
                        TestCleanFunction();
                        break;
                    case "4":
                        TestRestoreFunction();
                        break;
                    case "5":
                        TestLicenseManagement();
                        break;
                    case "6":
                        TestLogViewing();
                        break;
                    case "7":
                        Console.WriteLine("程序退出。");
                        return;
                    default:
                        Console.WriteLine("无效选项，请重新选择。");
                        break;
                }
                Console.WriteLine();
            }
        }

        static void TestScanFunction()
        {
            Console.WriteLine("🔍 扫描 Cursor 相关数据...");
            Console.WriteLine();
            
            Console.WriteLine("📁 扫描文件系统...");
            System.Threading.Thread.Sleep(1000);
            
            Console.WriteLine("✅ 找到: Cursor 应用数据");
            Console.WriteLine("   路径: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor");
            Console.WriteLine("   大小: 15.2 MB");
            Console.WriteLine();
            
            Console.WriteLine("✅ 找到: Cursor 本地数据");
            Console.WriteLine("   路径: C:\\Users\\<USER>\\AppData\\Local\\Cursor");
            Console.WriteLine("   大小: 128.5 MB");
            Console.WriteLine();
            
            Console.WriteLine("🗃️ 扫描注册表...");
            System.Threading.Thread.Sleep(500);
            
            Console.WriteLine("✅ 找到注册表项: Cursor 主配置");
            Console.WriteLine("   路径: HKEY_CURRENT_USER\\Software\\Cursor");
            Console.WriteLine("   子键: 5, 值: 12");
            Console.WriteLine();
            
            Console.WriteLine("📊 扫描结果汇总:");
            Console.WriteLine("📁 找到 3 个文件/目录");
            Console.WriteLine("🗃️ 找到 2 个注册表项");
            Console.WriteLine();
            Console.WriteLine("💡 提示：可以使用备份功能保存这些数据，然后使用清理功能删除它们。");
        }

        static void TestBackupFunction()
        {
            Console.WriteLine("💾 备份 Cursor 数据...");
            Console.WriteLine();
            
            Console.WriteLine("🔍 扫描需要备份的数据...");
            System.Threading.Thread.Sleep(1000);
            
            var backupPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), 
                "CursorBackup", $"CursorBackup_{DateTime.Now:yyyyMMdd_HHmmss}.zip");
            
            Console.WriteLine($"💾 创建备份文件: {backupPath}");
            Console.WriteLine();
            
            Console.WriteLine("📁 备份目录: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor");
            Console.WriteLine("📄 备份文件: C:\\Users\\<USER>\\Desktop\\Cursor.lnk");
            Console.WriteLine("🗃️ 备份注册表...");
            
            System.Threading.Thread.Sleep(2000);
            
            Console.WriteLine();
            Console.WriteLine("✅ 备份完成！");
            Console.WriteLine($"📁 备份文件位置: {backupPath}");
        }

        static void TestCleanFunction()
        {
            Console.WriteLine("⚠️  警告：即将清理 Cursor 数据！");
            Console.WriteLine("这将删除所有 Cursor 相关的配置、缓存和注册表项。");
            Console.Write("确定要继续吗？(输入 'YES' 确认): ");
            
            var confirmation = Console.ReadLine();
            if (confirmation != "YES")
            {
                Console.WriteLine("操作已取消。");
                return;
            }
            
            Console.WriteLine();
            Console.WriteLine("🧹 开始清理 Cursor 数据...");
            Console.WriteLine("⚠️ 请确保已经备份重要数据！");
            Console.WriteLine();
            
            Console.WriteLine("即将清理 3 个文件/目录和 2 个注册表项");
            Console.Write("最后确认：输入 'CONFIRM' 继续清理操作：");
            
            var finalConfirm = Console.ReadLine();
            if (finalConfirm != "CONFIRM")
            {
                Console.WriteLine("操作已取消。");
                return;
            }
            
            Console.WriteLine();
            Console.WriteLine("🔄 尝试关闭 Cursor 相关进程...");
            System.Threading.Thread.Sleep(1000);
            Console.WriteLine("✅ 进程已关闭: Cursor");
            Console.WriteLine();
            
            Console.WriteLine("🗂️ 清理文件和目录...");
            System.Threading.Thread.Sleep(1500);
            Console.WriteLine("✅ 目录已删除: C:\\Users\\<USER>\\AppData\\Roaming\\Cursor");
            Console.WriteLine();
            
            Console.WriteLine("🗃️ 清理注册表...");
            System.Threading.Thread.Sleep(1000);
            Console.WriteLine("✅ 注册表项已删除: HKEY_CURRENT_USER\\Software\\Cursor");
            Console.WriteLine();
            
            Console.WriteLine("🧹 清理其他相关项...");
            System.Threading.Thread.Sleep(500);
            Console.WriteLine("✅ 删除临时文件");
            Console.WriteLine("✅ 删除最近文件链接");
            Console.WriteLine();
            
            Console.WriteLine("✅ 清理完成！");
            Console.WriteLine("💡 建议重启计算机以确保所有更改生效。");
        }

        static void TestRestoreFunction()
        {
            Console.WriteLine("🔄 恢复备份...");
            Console.WriteLine();
            
            Console.WriteLine("🔍 查找备份文件...");
            System.Threading.Thread.Sleep(1000);
            
            Console.WriteLine("📋 可用的备份文件:");
            Console.WriteLine("1. CursorBackup_20241219_103000.zip (2024-12-19 10:30:00)");
            Console.WriteLine("2. CursorBackup_20241218_154500.zip (2024-12-18 15:45:00)");
            Console.WriteLine();
            
            Console.Write("请选择要恢复的备份 (输入编号): ");
            var choice = Console.ReadLine();
            
            if (choice == "1" || choice == "2")
            {
                Console.WriteLine($"🔄 恢复备份: CursorBackup_20241219_103000.zip");
                System.Threading.Thread.Sleep(2000);
                Console.WriteLine("✅ 恢复完成！");
            }
            else
            {
                Console.WriteLine("❌ 无效的选择。");
            }
        }

        static void TestLicenseManagement()
        {
            Console.WriteLine("=== 许可证管理 ===");
            Console.WriteLine();
            Console.WriteLine("✅ 许可证状态: 已激活");
            Console.WriteLine("📧 用户邮箱: <EMAIL>");
            Console.WriteLine("👤 用户名称: Test User");
            Console.WriteLine("🔑 许可证密钥: DEMO-1234-5678-9ABC");
            Console.WriteLine("📅 发行日期: 2024-01-01");
            Console.WriteLine("⏰ 到期日期: 2025-01-01");
            Console.WriteLine("🏷️ 许可证类型: 专业版");
            Console.WriteLine("💻 机器ID: ABC123DEF456GHI789JKL012");
            Console.WriteLine("🔢 版本: 1.0.0");
            Console.WriteLine("⏳ 剩余天数: 365 天");
        }

        static void TestLogViewing()
        {
            Console.WriteLine("=== 日志管理 ===");
            Console.WriteLine();
            Console.WriteLine("📁 日志目录: C:\\Users\\<USER>\\AppData\\Roaming\\CursorResetTool\\logs\\");
            Console.WriteLine();
            Console.WriteLine("=== 今日日志 (最后10行) ===");
            Console.WriteLine();
            
            var logEntries = new[]
            {
                "[2024-12-19 10:30:15.123] [Info] Application started successfully",
                "[2024-12-19 10:30:16.456] [Info] Administrator privileges confirmed",
                "[2024-12-19 10:30:17.789] [Info] License check completed",
                "[2024-12-19 10:30:20.012] [Info] User Action: Scan Cursor Data",
                "[2024-12-19 10:30:25.345] [Info] Cursor data scan completed successfully",
                "[2024-12-19 10:30:30.678] [Info] User Action: Backup Cursor Data",
                "[2024-12-19 10:30:35.901] [Info] Cursor data backup completed successfully",
                "[2024-12-19 10:30:40.234] [Warning] User confirmation for clean operation: YES",
                "[2024-12-19 10:30:45.567] [Info] Cursor data clean completed successfully",
                "[2024-12-19 10:30:50.890] [Info] Application ended"
            };
            
            foreach (var entry in logEntries)
            {
                if (entry.Contains("[Warning]"))
                {
                    Console.ForegroundColor = ConsoleColor.Yellow;
                }
                else if (entry.Contains("[Info]"))
                {
                    Console.ForegroundColor = ConsoleColor.Green;
                }
                else
                {
                    Console.ForegroundColor = ConsoleColor.Gray;
                }
                
                Console.WriteLine(entry);
                Console.ResetColor();
            }
            
            Console.WriteLine();
            Console.WriteLine($"📊 日志统计: 总共 {logEntries.Length} 行");
        }
    }
}
