<Window x:Class="CursorResetTool.Commercial.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="尚书_CAR_cursor重置系统 - 登录"
        Width="500" Height="700"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">

    <Border Background="{StaticResource SurfaceBrush}"
            CornerRadius="16"
            BorderThickness="1"
            BorderBrush="{StaticResource PrimaryBrush}">
        <Border.Effect>
            <DropShadowEffect Color="Black" Opacity="0.3" ShadowDepth="8" BlurRadius="20"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Border Grid.Row="0" 
                   Background="{StaticResource PrimaryBrush}"
                   CornerRadius="16,16,0,0"
                   Height="60">
                <Grid>
                    <TextBlock Text="尚书_CAR_cursor重置系统"
                              Style="{StaticResource TitleTextStyle}"
                              FontSize="18"
                              Foreground="White"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"/>
                    
                    <Button x:Name="CloseButton"
                           Style="{StaticResource IconButtonStyle}"
                           Width="30" Height="30"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           Margin="15"
                           Foreground="White"
                           Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                    </Button>
                </Grid>
            </Border>

            <!-- 主要内容 -->
            <ScrollViewer Grid.Row="1" 
                         VerticalScrollBarVisibility="Auto"
                         Padding="40">
                <StackPanel>
                    
                    <!-- Logo区域 -->
                    <Border Width="100" Height="100"
                           Background="{StaticResource PrimaryBrush}"
                           CornerRadius="50"
                           Margin="0,20,0,30"
                           HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="CursorDefault"
                                               Width="50" Height="50"
                                               Foreground="White"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                    </Border>

                    <!-- 欢迎文本 -->
                    <TextBlock Text="欢迎使用"
                              Style="{StaticResource TitleTextStyle}"
                              FontSize="24"
                              HorizontalAlignment="Center"
                              Margin="0,0,0,8"/>

                    <TextBlock Text="专业的Cursor编辑器重置工具"
                              Style="{StaticResource SubtitleTextStyle}"
                              FontSize="14"
                              HorizontalAlignment="Center"
                              Margin="0,0,0,40"/>

                    <!-- 登录表单 -->
                    <Border Style="{StaticResource CardStyle}"
                           Padding="30"
                           Margin="0,0,0,20">
                        <StackPanel>
                            
                            <!-- 用户名/邮箱 -->
                            <TextBox x:Name="UsernameTextBox"
                                    Style="{StaticResource ModernTextBoxStyle}"
                                    materialDesign:HintAssist.Hint="用户名或邮箱"
                                    materialDesign:HintAssist.IsFloating="True"
                                    Margin="0,0,0,20"/>

                            <!-- 密码 -->
                            <PasswordBox x:Name="PasswordBox"
                                        Style="{StaticResource ModernPasswordBoxStyle}"
                                        materialDesign:HintAssist.Hint="密码"
                                        materialDesign:HintAssist.IsFloating="True"
                                        Margin="0,0,0,20"/>

                            <!-- 记住我 -->
                            <CheckBox x:Name="RememberMeCheckBox"
                                     Content="记住我"
                                     Style="{StaticResource ModernCheckBoxStyle}"
                                     Margin="0,0,0,30"/>

                            <!-- 登录按钮 -->
                            <Button x:Name="LoginButton"
                                   Content="登录"
                                   Style="{StaticResource PrimaryButtonStyle}"
                                   Height="45"
                                   FontSize="16"
                                   Margin="0,0,0,15"
                                   Click="LoginButton_Click"/>

                            <!-- 忘记密码 -->
                            <Button x:Name="ForgotPasswordButton"
                                   Content="忘记密码？"
                                   Style="{StaticResource SecondaryButtonStyle}"
                                   Height="35"
                                   FontSize="14"
                                   Click="ForgotPasswordButton_Click"/>

                        </StackPanel>
                    </Border>

                    <!-- 试用和注册 -->
                    <Border Style="{StaticResource CardStyle}"
                           Padding="30">
                        <StackPanel>
                            
                            <TextBlock Text="还没有账户？"
                                      Style="{StaticResource BodyTextStyle}"
                                      HorizontalAlignment="Center"
                                      Margin="0,0,0,15"/>

                            <Button x:Name="RegisterButton"
                                   Content="立即注册"
                                   Style="{StaticResource SecondaryButtonStyle}"
                                   Height="40"
                                   Margin="0,0,0,15"
                                   Click="RegisterButton_Click"/>

                            <Button x:Name="TrialButton"
                                   Content="免费试用 7 天"
                                   Style="{StaticResource SuccessButtonStyle}"
                                   Height="40"
                                   Click="TrialButton_Click"/>

                        </StackPanel>
                    </Border>

                </StackPanel>
            </ScrollViewer>

            <!-- 底部状态栏 -->
            <Border Grid.Row="2"
                   Background="{StaticResource BackgroundBrush}"
                   CornerRadius="0,0,16,16"
                   Height="40">
                <Grid>
                    <TextBlock x:Name="StatusText"
                              Text="就绪"
                              Style="{StaticResource BodyTextStyle}"
                              FontSize="12"
                              Foreground="{StaticResource TextSecondaryBrush}"
                              VerticalAlignment="Center"
                              Margin="20,0"/>

                    <StackPanel Orientation="Horizontal"
                               HorizontalAlignment="Right"
                               VerticalAlignment="Center"
                               Margin="20,0">
                        
                        <Button x:Name="SettingsButton"
                               Style="{StaticResource IconButtonStyle}"
                               Width="24" Height="24"
                               ToolTip="设置"
                               Click="SettingsButton_Click">
                            <materialDesign:PackIcon Kind="Settings" Width="12" Height="12"/>
                        </Button>

                        <Button x:Name="HelpButton"
                               Style="{StaticResource IconButtonStyle}"
                               Width="24" Height="24"
                               ToolTip="帮助"
                               Margin="5,0,0,0"
                               Click="HelpButton_Click">
                            <materialDesign:PackIcon Kind="Help" Width="12" Height="12"/>
                        </Button>

                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </Border>
</Window>
