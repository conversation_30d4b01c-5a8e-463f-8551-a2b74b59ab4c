﻿#pragma checksum "..\..\..\..\Views\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "04C95A89AF9C652066B9CBD687BB0EA702E0E917"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace CursorResetTool.Commercial.Views {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 43 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserInfoText;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LogoutButton;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ScanButton;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackupButton;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CleanButton;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RestoreButton;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LicenseTypeText;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LicenseExpiryText;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewLogsButton;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OperationTitle;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ContentPanel;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OSText;
        
        #line default
        #line hidden
        
        
        #line 264 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ArchText;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MachineCodeText;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar OperationProgress;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/CursorResetTool-Commercial;V2.0.0.0;component/views/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UserInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 3:
            this.LogoutButton = ((System.Windows.Controls.Button)(target));
            
            #line 64 "..\..\..\..\Views\MainWindow.xaml"
            this.LogoutButton.Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ScanButton = ((System.Windows.Controls.Button)(target));
            
            #line 94 "..\..\..\..\Views\MainWindow.xaml"
            this.ScanButton.Click += new System.Windows.RoutedEventHandler(this.ScanButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 106 "..\..\..\..\Views\MainWindow.xaml"
            this.BackupButton.Click += new System.Windows.RoutedEventHandler(this.BackupButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CleanButton = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\..\Views\MainWindow.xaml"
            this.CleanButton.Click += new System.Windows.RoutedEventHandler(this.CleanButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.RestoreButton = ((System.Windows.Controls.Button)(target));
            
            #line 130 "..\..\..\..\Views\MainWindow.xaml"
            this.RestoreButton.Click += new System.Windows.RoutedEventHandler(this.RestoreButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.LicenseTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.LicenseExpiryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ViewLogsButton = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\..\Views\MainWindow.xaml"
            this.ViewLogsButton.Click += new System.Windows.RoutedEventHandler(this.ViewLogsButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.OperationTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ContentPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.OSText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.ArchText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.MachineCodeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.OperationProgress = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 17:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

