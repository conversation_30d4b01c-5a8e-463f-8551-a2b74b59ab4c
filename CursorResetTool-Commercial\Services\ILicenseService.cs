using CursorResetTool.Commercial.Models;

namespace CursorResetTool.Commercial.Services
{
    /// <summary>
    /// 许可证服务接口
    /// </summary>
    public interface ILicenseService
    {
        /// <summary>
        /// 在线激活许可证
        /// </summary>
        Task<(bool success, License? license, string message)> ActivateLicenseOnlineAsync(string licenseKey, string userEmail);

        /// <summary>
        /// 离线激活许可证
        /// </summary>
        Task<(bool success, License? license, string message)> ActivateLicenseOfflineAsync(string licenseKey, string userEmail);

        /// <summary>
        /// 验证许可证
        /// </summary>
        Task<(bool isValid, License? license, string message)> ValidateLicenseAsync();

        /// <summary>
        /// 获取当前许可证
        /// </summary>
        License? GetCurrentLicense();

        /// <summary>
        /// 设置当前许可证
        /// </summary>
        void SetCurrentLicense(License license);

        /// <summary>
        /// 生成试用许可证
        /// </summary>
        Task<(bool success, License? license, string message)> GenerateTrialLicenseAsync(string userEmail);

        /// <summary>
        /// 检查许可证到期
        /// </summary>
        (bool isExpiring, int daysRemaining) CheckLicenseExpiry();

        /// <summary>
        /// 续费许可证
        /// </summary>
        Task<(bool success, string message)> RenewLicenseAsync(string licenseKey);

        /// <summary>
        /// 撤销许可证
        /// </summary>
        Task<(bool success, string message)> RevokeLicenseAsync(string licenseKey);

        /// <summary>
        /// 获取许可证详细信息
        /// </summary>
        Task<License?> GetLicenseDetailsAsync(string licenseKey);

        /// <summary>
        /// 检查设备授权
        /// </summary>
        bool IsDeviceAuthorized();

        /// <summary>
        /// 保存许可证到本地
        /// </summary>
        Task<bool> SaveLicenseLocallyAsync(License license);

        /// <summary>
        /// 从本地加载许可证
        /// </summary>
        Task<License?> LoadLicenseLocallyAsync();

        /// <summary>
        /// 清除本地许可证
        /// </summary>
        Task<bool> ClearLocalLicenseAsync();
    }

    /// <summary>
    /// 许可证服务实现
    /// </summary>
    public class LicenseService : ILicenseService
    {
        private readonly ISecurityService _securityService;
        private readonly IMachineCodeService _machineCodeService;
        private readonly INetworkService _networkService;
        private License? _currentLicense;
        private readonly string _licenseDataPath;

        public LicenseService(
            ISecurityService securityService,
            IMachineCodeService machineCodeService,
            INetworkService networkService)
        {
            _securityService = securityService;
            _machineCodeService = machineCodeService;
            _networkService = networkService;
            _licenseDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "尚书CAR", "License");
            Directory.CreateDirectory(_licenseDataPath);
        }

        public async Task<(bool success, License? license, string message)> ActivateLicenseOnlineAsync(string licenseKey, string userEmail)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(licenseKey))
                    return (false, null, "请输入许可证密钥");

                if (string.IsNullOrWhiteSpace(userEmail))
                    return (false, null, "请输入邮箱地址");

                // 生成机器码和硬件指纹
                var machineCode = _machineCodeService.GenerateMachineCode();
                var hardwareFingerprint = _machineCodeService.GenerateHardwareFingerprint();

                // 网络激活
                var result = await _networkService.ActivateLicenseAsync(licenseKey, userEmail, machineCode, hardwareFingerprint);
                
                if (result.success && result.license != null)
                {
                    // 验证许可证
                    if (ValidateLicenseIntegrity(result.license))
                    {
                        _currentLicense = result.license;
                        await SaveLicenseLocallyAsync(_currentLicense);
                        return (true, _currentLicense, "许可证激活成功");
                    }
                    else
                    {
                        return (false, null, "许可证验证失败");
                    }
                }

                return (false, null, result.message);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Online license activation failed");
                return (false, null, "网络激活失败，请检查网络连接或尝试离线激活");
            }
        }

        public async Task<(bool success, License? license, string message)> ActivateLicenseOfflineAsync(string licenseKey, string userEmail)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(licenseKey))
                    return (false, null, "请输入许可证密钥");

                if (string.IsNullOrWhiteSpace(userEmail))
                    return (false, null, "请输入邮箱地址");

                // 生成机器码
                var machineCode = _machineCodeService.GenerateMachineCode();
                var hardwareFingerprint = _machineCodeService.GenerateHardwareFingerprint();

                // 创建离线许可证
                var license = CreateOfflineLicense(licenseKey, userEmail, machineCode, hardwareFingerprint);
                
                if (license != null)
                {
                    _currentLicense = license;
                    await SaveLicenseLocallyAsync(_currentLicense);
                    return (true, _currentLicense, "离线许可证激活成功");
                }

                return (false, null, "无效的许可证密钥");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Offline license activation failed");
                return (false, null, "离线激活失败");
            }
        }

        public async Task<(bool isValid, License? license, string message)> ValidateLicenseAsync()
        {
            try
            {
                // 先检查本地许可证
                if (_currentLicense == null)
                {
                    _currentLicense = await LoadLicenseLocallyAsync();
                }

                if (_currentLicense == null)
                    return (false, null, "未找到许可证");

                // 检查许可证基本有效性
                if (!_currentLicense.IsValid)
                {
                    var reason = GetInvalidReason(_currentLicense);
                    return (false, _currentLicense, reason);
                }

                // 检查机器码
                if (!_machineCodeService.ValidateMachineCode(_currentLicense.MachineCode))
                {
                    return (false, _currentLicense, "设备验证失败，许可证与当前设备不匹配");
                }

                // 尝试网络验证
                try
                {
                    var networkResult = await _networkService.ValidateLicenseAsync(_currentLicense.LicenseKey);
                    if (networkResult.isValid)
                    {
                        _currentLicense.LastValidationTime = DateTime.Now;
                        _currentLicense.ValidationCount++;
                        await SaveLicenseLocallyAsync(_currentLicense);
                    }
                }
                catch
                {
                    // 网络验证失败，使用本地验证
                    Serilog.Log.Warning("Network license validation failed, using local validation");
                }

                return (true, _currentLicense, "许可证有效");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "License validation failed");
                return (false, null, "许可证验证失败");
            }
        }

        public License? GetCurrentLicense() => _currentLicense;

        public void SetCurrentLicense(License license) => _currentLicense = license;

        public async Task<(bool success, License? license, string message)> GenerateTrialLicenseAsync(string userEmail)
        {
            try
            {
                var machineCode = _machineCodeService.GenerateMachineCode();
                var hardwareFingerprint = _machineCodeService.GenerateHardwareFingerprint();

                var trialLicense = new License
                {
                    LicenseId = Guid.NewGuid().ToString(),
                    LicenseKey = $"TRIAL-{_securityService.GenerateRandomString(8)}-{_securityService.GenerateRandomString(8)}",
                    UserEmail = userEmail,
                    LicenseType = LicenseType.Trial,
                    IssueDate = DateTime.Now,
                    ExpiryDate = DateTime.Now.AddDays(7), // 7天试用期
                    MaxDevices = 1,
                    ActivatedDevices = 1,
                    IsActive = true,
                    ActivationTime = DateTime.Now,
                    MachineCode = machineCode,
                    HardwareFingerprint = hardwareFingerprint,
                    Status = LicenseStatus.Active
                };

                // 生成数字签名
                var signatureData = $"{trialLicense.LicenseKey}|{trialLicense.UserEmail}|{trialLicense.ExpiryDate:yyyy-MM-dd}|{machineCode}";
                var (publicKey, privateKey) = _securityService.GenerateRSAKeyPair();
                trialLicense.DigitalSignature = _securityService.GenerateDigitalSignature(signatureData, privateKey);

                _currentLicense = trialLicense;
                await SaveLicenseLocallyAsync(_currentLicense);

                return (true, _currentLicense, "试用许可证生成成功");
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Trial license generation failed");
                return (false, null, "试用许可证生成失败");
            }
        }

        public (bool isExpiring, int daysRemaining) CheckLicenseExpiry()
        {
            if (_currentLicense == null || !_currentLicense.IsValid)
                return (false, 0);

            var daysRemaining = (_currentLicense.ExpiryDate - DateTime.Now).Days;
            var isExpiring = daysRemaining <= 7; // 7天内到期视为即将到期

            return (isExpiring, Math.Max(0, daysRemaining));
        }

        public async Task<(bool success, string message)> RenewLicenseAsync(string licenseKey)
        {
            try
            {
                var result = await _networkService.RenewLicenseAsync(licenseKey);
                if (result.success && result.license != null)
                {
                    _currentLicense = result.license;
                    await SaveLicenseLocallyAsync(_currentLicense);
                }
                return (result.success, result.message);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "License renewal failed");
                return (false, "许可证续费失败");
            }
        }

        public async Task<(bool success, string message)> RevokeLicenseAsync(string licenseKey)
        {
            try
            {
                var result = await _networkService.RevokeLicenseAsync(licenseKey);
                if (result.success)
                {
                    await ClearLocalLicenseAsync();
                    _currentLicense = null;
                }
                return (result.success, result.message);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "License revocation failed");
                return (false, "许可证撤销失败");
            }
        }

        public async Task<License?> GetLicenseDetailsAsync(string licenseKey)
        {
            try
            {
                return await _networkService.GetLicenseDetailsAsync(licenseKey);
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to get license details");
                return null;
            }
        }

        public bool IsDeviceAuthorized()
        {
            if (_currentLicense == null) return false;

            var currentMachineCode = _machineCodeService.GenerateMachineCode();
            return _currentLicense.MachineCode == currentMachineCode;
        }

        public async Task<bool> SaveLicenseLocallyAsync(License license)
        {
            try
            {
                var licensePath = Path.Combine(_licenseDataPath, "license.dat");
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(license, Newtonsoft.Json.Formatting.Indented);
                var encryptedJson = _securityService.EncryptAES(json, "LicenseData2024");
                await File.WriteAllTextAsync(licensePath, encryptedJson);

                // 创建隐藏的备份文件
                var backupPath = Path.Combine(_licenseDataPath, ".license.bak");
                await File.WriteAllTextAsync(backupPath, encryptedJson);
                File.SetAttributes(backupPath, FileAttributes.Hidden | FileAttributes.System);

                return true;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to save license locally");
                return false;
            }
        }

        public async Task<License?> LoadLicenseLocallyAsync()
        {
            try
            {
                var licensePath = Path.Combine(_licenseDataPath, "license.dat");
                if (!File.Exists(licensePath))
                {
                    // 尝试从备份恢复
                    var backupPath = Path.Combine(_licenseDataPath, ".license.bak");
                    if (File.Exists(backupPath))
                    {
                        licensePath = backupPath;
                    }
                    else
                    {
                        return null;
                    }
                }

                var encryptedJson = await File.ReadAllTextAsync(licensePath);
                var json = _securityService.DecryptAES(encryptedJson, "LicenseData2024");
                if (string.IsNullOrEmpty(json)) return null;

                var license = Newtonsoft.Json.JsonConvert.DeserializeObject<License>(json);
                return license;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to load license locally");
                return null;
            }
        }

        public async Task<bool> ClearLocalLicenseAsync()
        {
            try
            {
                var licensePath = Path.Combine(_licenseDataPath, "license.dat");
                var backupPath = Path.Combine(_licenseDataPath, ".license.bak");

                if (File.Exists(licensePath))
                    File.Delete(licensePath);

                if (File.Exists(backupPath))
                    File.Delete(backupPath);

                return true;
            }
            catch (Exception ex)
            {
                Serilog.Log.Error(ex, "Failed to clear local license");
                return false;
            }
        }

        private bool ValidateLicenseIntegrity(License license)
        {
            try
            {
                // 验证基本字段
                if (string.IsNullOrEmpty(license.LicenseKey) || 
                    string.IsNullOrEmpty(license.UserEmail) ||
                    string.IsNullOrEmpty(license.MachineCode))
                    return false;

                // 验证数字签名（如果有）
                if (!string.IsNullOrEmpty(license.DigitalSignature))
                {
                    var signatureData = $"{license.LicenseKey}|{license.UserEmail}|{license.ExpiryDate:yyyy-MM-dd}|{license.MachineCode}";
                    // 这里需要公钥来验证，实际应用中应该从安全的地方获取
                    // return _securityService.VerifyDigitalSignature(signatureData, license.DigitalSignature, publicKey);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        private License? CreateOfflineLicense(string licenseKey, string userEmail, string machineCode, string hardwareFingerprint)
        {
            // 这里实现离线许可证的创建逻辑
            // 可以基于预定义的许可证密钥格式来验证和创建许可证
            
            var knownLicenses = new Dictionary<string, (LicenseType type, int days, int maxDevices)>
            {
                ["DEMO-1234-5678-9ABC"] = (LicenseType.Professional, 365, 3),
                ["TEST-ABCD-EFGH-IJKL"] = (LicenseType.Personal, 180, 1),
                ["ENTERPRISE-2024-FULL"] = (LicenseType.Enterprise, 1095, 100)
            };

            if (knownLicenses.TryGetValue(licenseKey, out var licenseInfo))
            {
                return new License
                {
                    LicenseId = Guid.NewGuid().ToString(),
                    LicenseKey = licenseKey,
                    UserEmail = userEmail,
                    LicenseType = licenseInfo.type,
                    IssueDate = DateTime.Now,
                    ExpiryDate = DateTime.Now.AddDays(licenseInfo.days),
                    MaxDevices = licenseInfo.maxDevices,
                    ActivatedDevices = 1,
                    IsActive = true,
                    ActivationTime = DateTime.Now,
                    MachineCode = machineCode,
                    HardwareFingerprint = hardwareFingerprint,
                    Status = LicenseStatus.Active
                };
            }

            return null;
        }

        private string GetInvalidReason(License license)
        {
            if (!license.IsActive)
                return "许可证已被禁用";

            if (license.Status == LicenseStatus.Expired || DateTime.Now > license.ExpiryDate)
                return "许可证已过期";

            if (license.Status == LicenseStatus.Revoked)
                return "许可证已被撤销";

            if (license.Status == LicenseStatus.Suspended)
                return "许可证已被暂停";

            return "许可证无效";
        }
    }
}
