using System;
using System.IO;
using System.Diagnostics;
using Microsoft.Win32;
using CursorReset.Services;
using CursorReset.UI;

namespace CursorReset
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Cursor Reset Tool Pro (Licensed Version) ===");
            Console.WriteLine("警告：此工具仅用于学习目的！");
            Console.WriteLine("Warning: This tool is for educational purposes only!");
            Console.WriteLine();

            // 检查管理员权限
            if (!IsRunAsAdministrator())
            {
                Console.WriteLine("❌ 需要管理员权限运行此程序");
                Console.WriteLine("请右键点击程序，选择'以管理员身份运行'");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
                return;
            }

            Console.WriteLine("✅ 检测到管理员权限");
            Console.WriteLine();

            // 检查许可证
            if (!CheckLicense())
            {
                Console.WriteLine("程序退出。");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
                return;
            }

            // 显示菜单
            ShowMenu();
        }

        static void ShowMenu()
        {
            while (true)
            {
                ShowLicenseStatus();
                Console.WriteLine();
                Console.WriteLine("请选择操作：");
                Console.WriteLine("1. 扫描 Cursor 相关文件和注册表");
                Console.WriteLine("2. 备份 Cursor 数据");
                Console.WriteLine("3. 清理 Cursor 数据 (谨慎操作!)");
                Console.WriteLine("4. 恢复备份");
                Console.WriteLine("5. 许可证管理");
                Console.WriteLine("6. 退出");
                Console.Write("请输入选项 (1-6): ");

                string? choice = Console.ReadLine();
                Console.WriteLine();

                switch (choice)
                {
                    case "1":
                        ScanCursorData();
                        break;
                    case "2":
                        BackupCursorData();
                        break;
                    case "3":
                        CleanCursorData();
                        break;
                    case "4":
                        RestoreBackup();
                        break;
                    case "5":
                        LicenseManager.ShowLicenseMenu();
                        break;
                    case "6":
                        Console.WriteLine("程序退出。");
                        return;
                    default:
                        Console.WriteLine("无效选项，请重新选择。");
                        break;
                }
                Console.WriteLine();
            }
        }

        static bool IsRunAsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        static void ScanCursorData()
        {
            Console.WriteLine("🔍 扫描 Cursor 相关数据...");
            Console.WriteLine();

            var scanner = new CursorDataScanner();
            scanner.ScanAll();
        }

        static void BackupCursorData()
        {
            Console.WriteLine("💾 备份 Cursor 数据...");
            
            var backup = new CursorBackup();
            backup.CreateBackup();
        }

        static void CleanCursorData()
        {
            Console.WriteLine("⚠️  警告：即将清理 Cursor 数据！");
            Console.WriteLine("这将删除所有 Cursor 相关的配置、缓存和注册表项。");
            Console.Write("确定要继续吗？(输入 'YES' 确认): ");
            
            string? confirmation = Console.ReadLine();
            if (confirmation != "YES")
            {
                Console.WriteLine("操作已取消。");
                return;
            }

            var cleaner = new CursorDataCleaner();
            cleaner.CleanAll();
        }

        static void RestoreBackup()
        {
            Console.WriteLine("🔄 恢复备份...");

            var backup = new CursorBackup();
            backup.RestoreBackup();
        }

        static bool CheckLicense()
        {
            Console.WriteLine("🔍 检查许可证状态...");

            // 检查是否有有效许可证
            if (LicenseService.IsLicenseValid())
            {
                Console.WriteLine("✅ 许可证验证成功");
                return true;
            }

            // 检查试用期
            if (!LicenseService.IsTrialExpired())
            {
                var remainingDays = LicenseService.GetRemainingTrialDays();
                Console.WriteLine($"⏰ 试用期剩余 {remainingDays} 天");
                return true;
            }

            // 许可证无效或试用期过期
            Console.WriteLine("❌ 许可证无效或试用期已过期");
            Console.WriteLine();
            Console.WriteLine("请选择：");
            Console.WriteLine("1. 激活许可证");
            Console.WriteLine("2. 离线激活");
            Console.WriteLine("3. 申请试用");
            Console.WriteLine("4. 退出程序");
            Console.Write("请选择 (1-4): ");

            var choice = Console.ReadLine();
            Console.WriteLine();

            switch (choice)
            {
                case "1":
                case "2":
                case "3":
                    LicenseManager.ShowLicenseMenu();
                    return LicenseService.IsLicenseValid() || !LicenseService.IsTrialExpired();
                case "4":
                default:
                    return false;
            }
        }

        static void ShowLicenseStatus()
        {
            Console.Clear();
            Console.WriteLine("=== Cursor Reset Tool Pro ===");

            var license = LicenseService.LoadLicense();
            if (license != null && LicenseService.IsLicenseValid())
            {
                Console.WriteLine($"✅ 已授权 | 用户: {license.UserEmail} | 类型: {GetLicenseTypeText(license.Type)}");
                var remainingDays = (license.ExpiryDate - DateTime.Now).Days;
                if (remainingDays > 0)
                {
                    Console.WriteLine($"⏰ 许可证剩余: {remainingDays} 天");
                }
            }
            else if (!LicenseService.IsTrialExpired())
            {
                var remainingDays = LicenseService.GetRemainingTrialDays();
                Console.WriteLine($"🔓 试用版 | 剩余: {remainingDays} 天");
            }
            else
            {
                Console.WriteLine("❌ 未授权 | 请激活许可证");
            }
        }

        static string GetLicenseTypeText(Models.LicenseType type)
        {
            return type switch
            {
                Models.LicenseType.Trial => "试用版",
                Models.LicenseType.Personal => "个人版",
                Models.LicenseType.Professional => "专业版",
                Models.LicenseType.Enterprise => "企业版",
                _ => "未知"
            };
        }
    }
}
