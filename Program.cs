using System;
using System.IO;
using System.Diagnostics;
using Microsoft.Win32;
using CursorReset.Services;
using CursorReset.UI;

namespace CursorReset
{
    class Program
    {
        static void Main(string[] args)
        {
            try
            {
                // 初始化日志系统
                LoggingService.LogApplicationStart();
                LoggingService.LogUserAction("Application Started", $"Args: {string.Join(" ", args)}");

                Console.WriteLine("=== Cursor Reset Tool Pro (Licensed Version) ===");
                Console.WriteLine("警告：此工具仅用于学习目的！");
                Console.WriteLine("Warning: This tool is for educational purposes only!");
                Console.WriteLine();

                LoggingService.LogInfo("Application UI initialized");

                // 检查管理员权限
                using (new PerformanceTimer("Administrator Check"))
                {
                    if (!IsRunAsAdministrator())
                    {
                        LoggingService.LogWarning("Application started without administrator privileges");
                        Console.WriteLine("❌ 需要管理员权限运行此程序");
                        Console.WriteLine("请右键点击程序，选择'以管理员身份运行'");
                        Console.WriteLine("按任意键退出...");
                        Console.ReadKey();
                        return;
                    }

                    Console.WriteLine("✅ 检测到管理员权限");
                    LoggingService.LogInfo("Administrator privileges confirmed");
                }

                Console.WriteLine();

                // 检查许可证
                using (new PerformanceTimer("License Check"))
                {
                    if (!CheckLicense())
                    {
                        LoggingService.LogWarning("License check failed, application exiting");
                        Console.WriteLine("程序退出。");
                        Console.WriteLine("按任意键退出...");
                        Console.ReadKey();
                        return;
                    }
                }

                // 显示菜单
                LoggingService.LogInfo("Entering main menu");
                ShowMenu();
            }
            catch (Exception ex)
            {
                LoggingService.LogCritical("Unhandled exception in Main", ex);
                Console.WriteLine($"程序发生严重错误: {ex.Message}");
                Console.WriteLine("错误详情已记录到日志文件中。");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
            finally
            {
                LoggingService.LogApplicationEnd();
            }
        }

        static void ShowMenu()
        {
            while (true)
            {
                ShowLicenseStatus();
                Console.WriteLine();
                Console.WriteLine("请选择操作：");
                Console.WriteLine("1. 扫描 Cursor 相关文件和注册表");
                Console.WriteLine("2. 备份 Cursor 数据");
                Console.WriteLine("3. 清理 Cursor 数据 (谨慎操作!)");
                Console.WriteLine("4. 恢复备份");
                Console.WriteLine("5. 许可证管理");
                Console.WriteLine("6. 查看日志");
                Console.WriteLine("7. 退出");
                Console.Write("请输入选项 (1-7): ");

                string? choice = Console.ReadLine();
                Console.WriteLine();

                switch (choice)
                {
                    case "1":
                        ScanCursorData();
                        break;
                    case "2":
                        BackupCursorData();
                        break;
                    case "3":
                        CleanCursorData();
                        break;
                    case "4":
                        RestoreBackup();
                        break;
                    case "5":
                        LicenseManager.ShowLicenseMenu();
                        break;
                    case "6":
                        ShowLogMenu();
                        break;
                    case "7":
                        Console.WriteLine("程序退出。");
                        LoggingService.LogUserAction("Application Exit", "User selected exit from main menu");
                        return;
                    default:
                        Console.WriteLine("无效选项，请重新选择。");
                        LoggingService.LogWarning($"Invalid menu option selected: {choice}");
                        break;
                }
                Console.WriteLine();
            }
        }

        static bool IsRunAsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        static void ScanCursorData()
        {
            try
            {
                LoggingService.LogUserAction("Scan Cursor Data", "User initiated scan operation");
                Console.WriteLine("🔍 扫描 Cursor 相关数据...");
                Console.WriteLine();

                using (new PerformanceTimer("Cursor Data Scan"))
                {
                    var scanner = new CursorDataScanner();
                    scanner.ScanAll();
                }

                LoggingService.LogInfo("Cursor data scan completed successfully");
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to scan Cursor data", ex);
                Console.WriteLine($"❌ 扫描过程中发生错误: {ex.Message}");
            }
        }

        static void BackupCursorData()
        {
            try
            {
                LoggingService.LogUserAction("Backup Cursor Data", "User initiated backup operation");
                Console.WriteLine("💾 备份 Cursor 数据...");

                using (new PerformanceTimer("Cursor Data Backup"))
                {
                    var backup = new CursorBackup();
                    backup.CreateBackup();
                }

                LoggingService.LogInfo("Cursor data backup completed successfully");
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to backup Cursor data", ex);
                Console.WriteLine($"❌ 备份过程中发生错误: {ex.Message}");
            }
        }

        static void CleanCursorData()
        {
            try
            {
                LoggingService.LogUserAction("Clean Cursor Data", "User initiated clean operation");
                Console.WriteLine("⚠️  警告：即将清理 Cursor 数据！");
                Console.WriteLine("这将删除所有 Cursor 相关的配置、缓存和注册表项。");
                Console.Write("确定要继续吗？(输入 'YES' 确认): ");

                string? confirmation = Console.ReadLine();
                LoggingService.LogInfo($"User confirmation for clean operation: {confirmation}");

                if (confirmation != "YES")
                {
                    Console.WriteLine("操作已取消。");
                    LoggingService.LogInfo("Clean operation cancelled by user");
                    return;
                }

                using (new PerformanceTimer("Cursor Data Clean"))
                {
                    var cleaner = new CursorDataCleaner();
                    cleaner.CleanAll();
                }

                LoggingService.LogInfo("Cursor data clean completed successfully");
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to clean Cursor data", ex);
                Console.WriteLine($"❌ 清理过程中发生错误: {ex.Message}");
            }
        }

        static void RestoreBackup()
        {
            try
            {
                LoggingService.LogUserAction("Restore Backup", "User initiated restore operation");
                Console.WriteLine("🔄 恢复备份...");

                using (new PerformanceTimer("Cursor Data Restore"))
                {
                    var backup = new CursorBackup();
                    backup.RestoreBackup();
                }

                LoggingService.LogInfo("Cursor data restore completed successfully");
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to restore backup", ex);
                Console.WriteLine($"❌ 恢复过程中发生错误: {ex.Message}");
            }
        }

        static bool CheckLicense()
        {
            Console.WriteLine("🔍 检查许可证状态...");

            // 检查是否有有效许可证
            if (LicenseService.IsLicenseValid())
            {
                Console.WriteLine("✅ 许可证验证成功");
                return true;
            }

            // 检查试用期
            if (!LicenseService.IsTrialExpired())
            {
                var remainingDays = LicenseService.GetRemainingTrialDays();
                Console.WriteLine($"⏰ 试用期剩余 {remainingDays} 天");
                return true;
            }

            // 许可证无效或试用期过期
            Console.WriteLine("❌ 许可证无效或试用期已过期");
            Console.WriteLine();
            Console.WriteLine("请选择：");
            Console.WriteLine("1. 激活许可证");
            Console.WriteLine("2. 离线激活");
            Console.WriteLine("3. 申请试用");
            Console.WriteLine("4. 退出程序");
            Console.Write("请选择 (1-4): ");

            var choice = Console.ReadLine();
            Console.WriteLine();

            switch (choice)
            {
                case "1":
                case "2":
                case "3":
                    LicenseManager.ShowLicenseMenu();
                    return LicenseService.IsLicenseValid() || !LicenseService.IsTrialExpired();
                case "4":
                default:
                    return false;
            }
        }

        static void ShowLicenseStatus()
        {
            Console.Clear();
            Console.WriteLine("=== Cursor Reset Tool Pro ===");

            var license = LicenseService.LoadLicense();
            if (license != null && LicenseService.IsLicenseValid())
            {
                Console.WriteLine($"✅ 已授权 | 用户: {license.UserEmail} | 类型: {GetLicenseTypeText(license.Type)}");
                var remainingDays = (license.ExpiryDate - DateTime.Now).Days;
                if (remainingDays > 0)
                {
                    Console.WriteLine($"⏰ 许可证剩余: {remainingDays} 天");
                }
            }
            else if (!LicenseService.IsTrialExpired())
            {
                var remainingDays = LicenseService.GetRemainingTrialDays();
                Console.WriteLine($"🔓 试用版 | 剩余: {remainingDays} 天");
            }
            else
            {
                Console.WriteLine("❌ 未授权 | 请激活许可证");
            }
        }

        static void ShowLogMenu()
        {
            try
            {
                LoggingService.LogUserAction("View Logs", "User accessed log menu");

                while (true)
                {
                    Console.Clear();
                    Console.WriteLine("=== 日志管理 ===");
                    Console.WriteLine();
                    Console.WriteLine($"📁 日志目录: {LoggingService.GetLogDirectory()}");
                    Console.WriteLine();
                    Console.WriteLine("请选择操作：");
                    Console.WriteLine("1. 查看今日日志");
                    Console.WriteLine("2. 打开日志目录");
                    Console.WriteLine("3. 清理旧日志");
                    Console.WriteLine("4. 测试日志记录");
                    Console.WriteLine("5. 返回主菜单");
                    Console.Write("请选择 (1-5): ");

                    var choice = Console.ReadLine();
                    Console.WriteLine();

                    switch (choice)
                    {
                        case "1":
                            ShowTodayLogs();
                            break;
                        case "2":
                            LoggingService.OpenLogDirectory();
                            Console.WriteLine("✅ 已打开日志目录");
                            break;
                        case "3":
                            CleanOldLogs();
                            break;
                        case "4":
                            TestLogging();
                            break;
                        case "5":
                            return;
                        default:
                            Console.WriteLine("无效选项，请重新选择。");
                            break;
                    }

                    if (choice != "5")
                    {
                        Console.WriteLine("\n按任意键继续...");
                        Console.ReadKey();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Error in log menu", ex);
                Console.WriteLine($"❌ 日志菜单发生错误: {ex.Message}");
            }
        }

        static void ShowTodayLogs()
        {
            try
            {
                var logFile = Path.Combine(LoggingService.GetLogDirectory(), $"app_{DateTime.Now:yyyyMMdd}.log");

                if (!File.Exists(logFile))
                {
                    Console.WriteLine("❌ 今日日志文件不存在");
                    return;
                }

                Console.WriteLine("=== 今日日志 (最后50行) ===");
                Console.WriteLine();

                var lines = File.ReadAllLines(logFile);
                var startIndex = Math.Max(0, lines.Length - 50);

                for (int i = startIndex; i < lines.Length; i++)
                {
                    var line = lines[i];

                    // 根据日志级别设置颜色
                    if (line.Contains("[Error]") || line.Contains("[Critical]"))
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                    }
                    else if (line.Contains("[Warning]"))
                    {
                        Console.ForegroundColor = ConsoleColor.Yellow;
                    }
                    else if (line.Contains("[Info]"))
                    {
                        Console.ForegroundColor = ConsoleColor.Green;
                    }
                    else
                    {
                        Console.ForegroundColor = ConsoleColor.Gray;
                    }

                    Console.WriteLine(line);
                    Console.ResetColor();
                }

                Console.WriteLine();
                Console.WriteLine($"📊 日志统计: 总共 {lines.Length} 行");
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to show today's logs", ex);
                Console.WriteLine($"❌ 读取日志失败: {ex.Message}");
            }
        }

        static void CleanOldLogs()
        {
            try
            {
                Console.Write("请输入要保留的天数 (默认30天): ");
                var input = Console.ReadLine();

                if (!int.TryParse(input, out int daysToKeep))
                {
                    daysToKeep = 30;
                }

                LoggingService.CleanOldLogs(daysToKeep);
                Console.WriteLine($"✅ 已清理 {daysToKeep} 天前的日志文件");
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to clean old logs", ex);
                Console.WriteLine($"❌ 清理日志失败: {ex.Message}");
            }
        }

        static void TestLogging()
        {
            try
            {
                Console.WriteLine("🧪 测试日志记录功能...");
                Console.WriteLine();

                LoggingService.LogDebug("这是一条调试信息");
                LoggingService.LogInfo("这是一条信息日志");
                LoggingService.LogWarning("这是一条警告信息");
                LoggingService.LogError("这是一条错误信息", new Exception("测试异常"));

                using (new PerformanceTimer("Test Operation"))
                {
                    System.Threading.Thread.Sleep(100); // 模拟操作
                }

                LoggingService.LogUserAction("Test Action", "User triggered log test");

                Console.WriteLine("✅ 日志测试完成，请查看日志文件确认");
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to test logging", ex);
                Console.WriteLine($"❌ 日志测试失败: {ex.Message}");
            }
        }

        static string GetLicenseTypeText(Models.LicenseType type)
        {
            return type switch
            {
                Models.LicenseType.Trial => "试用版",
                Models.LicenseType.Personal => "个人版",
                Models.LicenseType.Professional => "专业版",
                Models.LicenseType.Enterprise => "企业版",
                _ => "未知"
            };
        }
    }
}
