using System;
using System.IO;
using System.Diagnostics;
using Microsoft.Win32;

namespace CursorReset
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Cursor Reset Tool (Educational Purpose) ===");
            Console.WriteLine("警告：此工具仅用于学习目的！");
            Console.WriteLine("Warning: This tool is for educational purposes only!");
            Console.WriteLine();

            // 检查管理员权限
            if (!IsRunAsAdministrator())
            {
                Console.WriteLine("❌ 需要管理员权限运行此程序");
                Console.WriteLine("请右键点击程序，选择'以管理员身份运行'");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
                return;
            }

            Console.WriteLine("✅ 检测到管理员权限");
            Console.WriteLine();

            // 显示菜单
            ShowMenu();
        }

        static void ShowMenu()
        {
            while (true)
            {
                Console.WriteLine("请选择操作：");
                Console.WriteLine("1. 扫描 Cursor 相关文件和注册表");
                Console.WriteLine("2. 备份 Cursor 数据");
                Console.WriteLine("3. 清理 Cursor 数据 (谨慎操作!)");
                Console.WriteLine("4. 恢复备份");
                Console.WriteLine("5. 退出");
                Console.Write("请输入选项 (1-5): ");

                string? choice = Console.ReadLine();
                Console.WriteLine();

                switch (choice)
                {
                    case "1":
                        ScanCursorData();
                        break;
                    case "2":
                        BackupCursorData();
                        break;
                    case "3":
                        CleanCursorData();
                        break;
                    case "4":
                        RestoreBackup();
                        break;
                    case "5":
                        Console.WriteLine("程序退出。");
                        return;
                    default:
                        Console.WriteLine("无效选项，请重新选择。");
                        break;
                }
                Console.WriteLine();
            }
        }

        static bool IsRunAsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        static void ScanCursorData()
        {
            Console.WriteLine("🔍 扫描 Cursor 相关数据...");
            Console.WriteLine();

            var scanner = new CursorDataScanner();
            scanner.ScanAll();
        }

        static void BackupCursorData()
        {
            Console.WriteLine("💾 备份 Cursor 数据...");
            
            var backup = new CursorBackup();
            backup.CreateBackup();
        }

        static void CleanCursorData()
        {
            Console.WriteLine("⚠️  警告：即将清理 Cursor 数据！");
            Console.WriteLine("这将删除所有 Cursor 相关的配置、缓存和注册表项。");
            Console.Write("确定要继续吗？(输入 'YES' 确认): ");
            
            string? confirmation = Console.ReadLine();
            if (confirmation != "YES")
            {
                Console.WriteLine("操作已取消。");
                return;
            }

            var cleaner = new CursorDataCleaner();
            cleaner.CleanAll();
        }

        static void RestoreBackup()
        {
            Console.WriteLine("🔄 恢复备份...");
            
            var backup = new CursorBackup();
            backup.RestoreBackup();
        }
    }
}
