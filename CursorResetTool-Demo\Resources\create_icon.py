#!/usr/bin/env python3
"""
创建尚书CAR图标的Python脚本
需要安装: pip install Pillow
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon():
    # 创建多个尺寸的图标
    sizes = [16, 24, 32, 48, 64, 128, 256]
    images = []
    
    for size in sizes:
        # 创建图像
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 计算尺寸比例
        scale = size / 256.0
        
        # 绘制背景圆形
        margin = int(8 * scale)
        circle_size = size - 2 * margin
        
        # 主色调 - 蓝色渐变
        primary_color = (25, 118, 210)  # Material Blue
        secondary_color = (33, 150, 243)  # Light Blue
        
        # 绘制主背景圆
        draw.ellipse([margin, margin, margin + circle_size, margin + circle_size], 
                    fill=primary_color)
        
        # 绘制内部高光
        highlight_margin = int(12 * scale)
        highlight_size = size - 2 * highlight_margin
        draw.ellipse([highlight_margin, highlight_margin, 
                     highlight_margin + highlight_size, highlight_margin + highlight_size], 
                    fill=secondary_color)
        
        # 绘制光标图标
        cursor_size = int(size * 0.4)
        cursor_x = (size - cursor_size) // 2
        cursor_y = (size - cursor_size) // 2
        
        # 绘制光标箭头
        arrow_points = [
            (cursor_x, cursor_y),
            (cursor_x, cursor_y + cursor_size),
            (cursor_x + cursor_size // 3, cursor_y + cursor_size * 2 // 3),
            (cursor_x + cursor_size // 2, cursor_y + cursor_size),
            (cursor_x + cursor_size, cursor_y + cursor_size // 2),
            (cursor_x + cursor_size // 2, cursor_y + cursor_size // 3),
        ]
        
        # 简化为三角形箭头
        triangle_points = [
            (cursor_x + cursor_size // 4, cursor_y + cursor_size // 4),
            (cursor_x + cursor_size // 4, cursor_y + cursor_size * 3 // 4),
            (cursor_x + cursor_size * 3 // 4, cursor_y + cursor_size // 2),
        ]
        
        draw.polygon(triangle_points, fill=(255, 255, 255))
        
        # 添加重置符号（圆形箭头）
        if size >= 32:
            reset_size = int(size * 0.15)
            reset_x = cursor_x + cursor_size - reset_size
            reset_y = cursor_y
            
            # 绘制小圆圈表示重置
            draw.ellipse([reset_x, reset_y, reset_x + reset_size, reset_y + reset_size], 
                        outline=(255, 255, 255), width=max(1, int(2 * scale)))
        
        images.append(img)
    
    return images

def save_icon(images, filename):
    """保存为ICO文件"""
    images[0].save(filename, format='ICO', sizes=[(img.width, img.height) for img in images])

def create_png_icon(size=256):
    """创建PNG版本的图标用于预览"""
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制背景圆形
    margin = 16
    circle_size = size - 2 * margin
    
    # 渐变效果
    primary_color = (25, 118, 210)
    secondary_color = (33, 150, 243)
    
    # 绘制主背景
    draw.ellipse([margin, margin, margin + circle_size, margin + circle_size], 
                fill=primary_color)
    
    # 绘制内部高光
    highlight_margin = 24
    highlight_size = size - 2 * highlight_margin
    draw.ellipse([highlight_margin, highlight_margin, 
                 highlight_margin + highlight_size, highlight_margin + highlight_size], 
                fill=secondary_color)
    
    # 绘制光标图标
    cursor_size = int(size * 0.35)
    cursor_x = (size - cursor_size) // 2
    cursor_y = (size - cursor_size) // 2
    
    # 绘制现代化的光标箭头
    arrow_width = cursor_size // 8
    
    # 主箭头
    arrow_points = [
        (cursor_x, cursor_y),
        (cursor_x + arrow_width, cursor_y + arrow_width),
        (cursor_x + arrow_width, cursor_y + cursor_size // 3),
        (cursor_x + cursor_size // 2, cursor_y + cursor_size // 3),
        (cursor_x + cursor_size // 2, cursor_y + cursor_size // 2),
        (cursor_x + cursor_size // 3, cursor_y + cursor_size // 2),
        (cursor_x + cursor_size // 3, cursor_y + arrow_width),
        (cursor_x + arrow_width, cursor_y + arrow_width),
    ]
    
    # 简化的箭头设计
    draw.polygon([
        (cursor_x + cursor_size // 3, cursor_y + cursor_size // 4),
        (cursor_x + cursor_size // 3, cursor_y + cursor_size // 2),
        (cursor_x + cursor_size // 2, cursor_y + cursor_size // 2),
        (cursor_x + cursor_size * 2 // 3, cursor_y + cursor_size // 3),
        (cursor_x + cursor_size // 2, cursor_y + cursor_size // 6),
    ], fill=(255, 255, 255))
    
    # 添加重置符号
    reset_center_x = cursor_x + cursor_size * 3 // 4
    reset_center_y = cursor_y + cursor_size // 4
    reset_radius = cursor_size // 8
    
    # 绘制圆形重置箭头
    draw.arc([reset_center_x - reset_radius, reset_center_y - reset_radius,
              reset_center_x + reset_radius, reset_center_y + reset_radius],
             start=45, end=315, fill=(255, 255, 255), width=4)
    
    # 添加箭头头部
    arrow_head_size = 6
    draw.polygon([
        (reset_center_x + reset_radius - 2, reset_center_y - reset_radius + 2),
        (reset_center_x + reset_radius + 2, reset_center_y - reset_radius - 2),
        (reset_center_x + reset_radius + 6, reset_center_y - reset_radius + 2),
    ], fill=(255, 255, 255))
    
    return img

if __name__ == "__main__":
    # 创建Resources目录
    os.makedirs(".", exist_ok=True)
    
    print("Creating icon images...")
    images = create_icon()
    
    print("Saving ICO file...")
    save_icon(images, "app_icon.ico")
    
    print("Creating PNG preview...")
    png_icon = create_png_icon()
    png_icon.save("app_icon_preview.png")
    
    print("Icon files created successfully!")
    print("- app_icon.ico (for application)")
    print("- app_icon_preview.png (for preview)")
