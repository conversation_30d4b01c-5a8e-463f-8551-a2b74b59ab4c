using System.ComponentModel.DataAnnotations;

namespace CursorResetTool.Commercial.Models
{
    /// <summary>
    /// 用户模型
    /// </summary>
    public class User
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "用户名长度必须在3-50个字符之间")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        [Required(ErrorMessage = "邮箱不能为空")]
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 密码哈希
        /// </summary>
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// 盐值
        /// </summary>
        public string Salt { get; set; } = string.Empty;

        /// <summary>
        /// 会员等级
        /// </summary>
        public MembershipLevel MembershipLevel { get; set; } = MembershipLevel.Trial;

        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime RegisterTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 激活码
        /// </summary>
        public string? ActivationCode { get; set; }

        /// <summary>
        /// 重置密码令牌
        /// </summary>
        public string? ResetPasswordToken { get; set; }

        /// <summary>
        /// 重置密码令牌过期时间
        /// </summary>
        public DateTime? ResetPasswordTokenExpiry { get; set; }

        /// <summary>
        /// 用户配置
        /// </summary>
        public UserSettings Settings { get; set; } = new UserSettings();
    }

    /// <summary>
    /// 会员等级枚举
    /// </summary>
    public enum MembershipLevel
    {
        /// <summary>
        /// 试用版
        /// </summary>
        Trial = 0,

        /// <summary>
        /// 个人版
        /// </summary>
        Personal = 1,

        /// <summary>
        /// 专业版
        /// </summary>
        Professional = 2,

        /// <summary>
        /// 企业版
        /// </summary>
        Enterprise = 3
    }

    /// <summary>
    /// 用户设置
    /// </summary>
    public class UserSettings
    {
        /// <summary>
        /// 语言设置
        /// </summary>
        public string Language { get; set; } = "zh-CN";

        /// <summary>
        /// 主题设置
        /// </summary>
        public string Theme { get; set; } = "Light";

        /// <summary>
        /// 自动检查更新
        /// </summary>
        public bool AutoCheckUpdate { get; set; } = true;

        /// <summary>
        /// 启动时显示启动画面
        /// </summary>
        public bool ShowSplashScreen { get; set; } = true;

        /// <summary>
        /// 记住登录状态
        /// </summary>
        public bool RememberLogin { get; set; } = false;

        /// <summary>
        /// 自动备份
        /// </summary>
        public bool AutoBackup { get; set; } = true;

        /// <summary>
        /// 备份保留天数
        /// </summary>
        public int BackupRetentionDays { get; set; } = 30;
    }
}
