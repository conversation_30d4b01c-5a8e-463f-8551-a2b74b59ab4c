@echo off
title WPF Application Launcher

echo ========================================
echo    Cursor Reset Tool - WPF Demo
echo ========================================
echo.

REM Get current directory
set CURRENT_DIR=%~dp0

REM Define program path
set PROGRAM_PATH=%CURRENT_DIR%CursorResetTool-Demo\bin\Debug\net8.0-windows\CursorResetTool-Demo.exe

echo Checking for program file...

if exist "%PROGRAM_PATH%" (
    echo [OK] Program file found
    echo.
    echo Starting WPF application...
    echo.
    
    REM Start the WPF application
    start "" "%PROGRAM_PATH%"
    
    echo ========================================
    echo    Application Started Successfully!
    echo ========================================
    echo.
    echo If the program interface doesn't appear:
    echo.
    echo 1. Install .NET 8.0 Desktop Runtime
    echo    Download: https://dotnet.microsoft.com/download/dotnet/8.0
    echo    Select: Desktop Runtime x64
    echo.
    echo 2. Check Task Manager for running process
    echo.
    echo 3. Ensure sufficient system permissions
    echo.
    echo ========================================
    echo.
    echo Program Features:
    echo - Modern Material Design interface
    echo - Complete Cursor reset functionality
    echo - Smart scanning and safe backup
    echo - Enterprise-grade user experience
    echo.
    
    timeout /t 5 /nobreak >nul
    
) else (
    echo [ERROR] Program file not found!
    echo.
    echo Expected path: %PROGRAM_PATH%
    echo.
    echo Please check:
    echo 1. CursorResetTool-Demo folder exists
    echo 2. Program has been compiled
    echo.
    echo To rebuild, run:
    echo   cd CursorResetTool-Demo
    echo   dotnet build
    echo.
    pause
)

exit
