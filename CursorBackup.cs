using System;
using System.IO;
using System.IO.Compression;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Win32;

namespace CursorReset
{
    public class CursorBackup
    {
        private readonly string _backupDirectory;
        private readonly string _backupFileName;

        public CursorBackup()
        {
            _backupDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "CursorBackup");
            _backupFileName = $"CursorBackup_{DateTime.Now:yyyyMMdd_HHmmss}.zip";
        }

        public void CreateBackup()
        {
            try
            {
                Console.WriteLine("🔍 扫描需要备份的数据...");
                var scanner = new CursorDataScanner();
                scanner.ScanAll();

                var foundFiles = scanner.GetFoundFiles();
                var foundRegistryKeys = scanner.GetFoundRegistryKeys();

                if (foundFiles.Count == 0 && foundRegistryKeys.Count == 0)
                {
                    Console.WriteLine("❌ 没有找到需要备份的数据。");
                    return;
                }

                // 创建备份目录
                Directory.CreateDirectory(_backupDirectory);
                var backupPath = Path.Combine(_backupDirectory, _backupFileName);

                Console.WriteLine($"💾 创建备份文件: {backupPath}");

                using var archive = ZipFile.Open(backupPath, ZipArchiveMode.Create);

                // 备份文件和目录
                foreach (var filePath in foundFiles)
                {
                    try
                    {
                        if (Directory.Exists(filePath))
                        {
                            BackupDirectory(archive, filePath);
                        }
                        else if (File.Exists(filePath))
                        {
                            BackupFile(archive, filePath);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ 备份文件失败 {filePath}: {ex.Message}");
                    }
                }

                // 备份注册表
                if (foundRegistryKeys.Count > 0)
                {
                    BackupRegistry(archive, foundRegistryKeys);
                }

                Console.WriteLine("✅ 备份完成！");
                Console.WriteLine($"📁 备份文件位置: {backupPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 备份过程中发生错误: {ex.Message}");
            }
        }

        private void BackupDirectory(ZipArchive archive, string directoryPath)
        {
            Console.WriteLine($"📁 备份目录: {directoryPath}");
            
            var dirInfo = new DirectoryInfo(directoryPath);
            var basePath = dirInfo.Parent?.FullName ?? "";
            
            foreach (var file in dirInfo.GetFiles("*", SearchOption.AllDirectories))
            {
                try
                {
                    var relativePath = Path.GetRelativePath(basePath, file.FullName);
                    var entry = archive.CreateEntry(relativePath);
                    
                    using var entryStream = entry.Open();
                    using var fileStream = file.OpenRead();
                    fileStream.CopyTo(entryStream);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 备份文件失败 {file.FullName}: {ex.Message}");
                }
            }
        }

        private void BackupFile(ZipArchive archive, string filePath)
        {
            Console.WriteLine($"📄 备份文件: {filePath}");
            
            try
            {
                var fileName = Path.GetFileName(filePath);
                var entry = archive.CreateEntry($"Files/{fileName}");
                
                using var entryStream = entry.Open();
                using var fileStream = File.OpenRead(filePath);
                fileStream.CopyTo(entryStream);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 备份文件失败 {filePath}: {ex.Message}");
            }
        }

        private void BackupRegistry(ZipArchive archive, List<string> registryKeys)
        {
            Console.WriteLine("🗃️ 备份注册表...");
            
            try
            {
                var regContent = "Windows Registry Editor Version 5.00\r\n\r\n";
                
                foreach (var keyPath in registryKeys)
                {
                    try
                    {
                        using var key = Registry.CurrentUser.OpenSubKey(keyPath);
                        if (key != null)
                        {
                            regContent += ExportRegistryKey(key, $"HKEY_CURRENT_USER\\{keyPath}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ 备份注册表项失败 {keyPath}: {ex.Message}");
                    }
                }
                
                var entry = archive.CreateEntry("Registry/cursor_registry_backup.reg");
                using var entryStream = entry.Open();
                using var writer = new StreamWriter(entryStream);
                writer.Write(regContent);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 备份注册表失败: {ex.Message}");
            }
        }

        private string ExportRegistryKey(RegistryKey key, string keyPath)
        {
            var content = $"[{keyPath}]\r\n";
            
            // 导出值
            foreach (var valueName in key.GetValueNames())
            {
                try
                {
                    var value = key.GetValue(valueName);
                    var valueKind = key.GetValueKind(valueName);
                    
                    content += FormatRegistryValue(valueName, value, valueKind);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 导出注册表值失败 {valueName}: {ex.Message}");
                }
            }
            
            content += "\r\n";
            
            // 递归导出子键
            foreach (var subKeyName in key.GetSubKeyNames())
            {
                try
                {
                    using var subKey = key.OpenSubKey(subKeyName);
                    if (subKey != null)
                    {
                        content += ExportRegistryKey(subKey, $"{keyPath}\\{subKeyName}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 导出子注册表项失败 {subKeyName}: {ex.Message}");
                }
            }
            
            return content;
        }

        private string FormatRegistryValue(string name, object? value, RegistryValueKind kind)
        {
            if (string.IsNullOrEmpty(name))
                name = "@";
            else
                name = $"\"{name}\"";
            
            return kind switch
            {
                RegistryValueKind.String => $"{name}=\"{value}\"\r\n",
                RegistryValueKind.DWord => $"{name}=dword:{value:x8}\r\n",
                RegistryValueKind.QWord => $"{name}=qword:{value:x16}\r\n",
                RegistryValueKind.Binary => $"{name}=hex:{FormatBinaryValue((byte[]?)value)}\r\n",
                RegistryValueKind.MultiString => $"{name}=hex(7):{FormatMultiStringValue((string[]?)value)}\r\n",
                RegistryValueKind.ExpandString => $"{name}=hex(2):{FormatExpandStringValue((string?)value)}\r\n",
                _ => $"{name}=\"{value}\"\r\n"
            };
        }

        private string FormatBinaryValue(byte[]? bytes)
        {
            if (bytes == null) return "";
            return string.Join(",", bytes.Select(b => b.ToString("x2")));
        }

        private string FormatMultiStringValue(string[]? strings)
        {
            if (strings == null) return "";
            var combined = string.Join("\0", strings) + "\0\0";
            var bytes = System.Text.Encoding.Unicode.GetBytes(combined);
            return string.Join(",", bytes.Select(b => b.ToString("x2")));
        }

        private string FormatExpandStringValue(string? value)
        {
            if (value == null) return "";
            var bytes = System.Text.Encoding.Unicode.GetBytes(value + "\0");
            return string.Join(",", bytes.Select(b => b.ToString("x2")));
        }

        public void RestoreBackup()
        {
            Console.WriteLine("🔍 查找备份文件...");
            
            if (!Directory.Exists(_backupDirectory))
            {
                Console.WriteLine("❌ 备份目录不存在。");
                return;
            }

            var backupFiles = Directory.GetFiles(_backupDirectory, "CursorBackup_*.zip")
                .OrderByDescending(f => File.GetCreationTime(f))
                .ToArray();

            if (backupFiles.Length == 0)
            {
                Console.WriteLine("❌ 没有找到备份文件。");
                return;
            }

            Console.WriteLine("📋 可用的备份文件:");
            for (int i = 0; i < backupFiles.Length; i++)
            {
                var fileName = Path.GetFileName(backupFiles[i]);
                var fileInfo = new FileInfo(backupFiles[i]);
                Console.WriteLine($"{i + 1}. {fileName} ({fileInfo.CreationTime:yyyy-MM-dd HH:mm:ss})");
            }

            Console.Write("请选择要恢复的备份 (输入编号): ");
            if (int.TryParse(Console.ReadLine(), out int choice) && choice > 0 && choice <= backupFiles.Length)
            {
                var selectedBackup = backupFiles[choice - 1];
                Console.WriteLine($"🔄 恢复备份: {Path.GetFileName(selectedBackup)}");
                
                // 这里可以实现恢复逻辑
                Console.WriteLine("⚠️ 恢复功能需要谨慎实现，建议手动恢复重要数据。");
                Console.WriteLine($"📁 备份文件位置: {selectedBackup}");
            }
            else
            {
                Console.WriteLine("❌ 无效的选择。");
            }
        }
    }
}
