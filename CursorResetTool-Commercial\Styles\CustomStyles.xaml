<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- 卡片样式 -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 主要按钮样式 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Padding" Value="24,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="20"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
    </Style>

    <!-- 次要按钮样式 -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Padding" Value="24,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="20"/>
    </Style>

    <!-- 危险按钮样式 -->
    <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Padding" Value="24,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="20"/>
    </Style>

    <!-- 成功按钮样式 -->
    <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Padding" Value="24,8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="20"/>
    </Style>

    <!-- 文本框样式 -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        <Setter Property="materialDesign:TextFieldAssist.HasOutlinedTextField" Value="True"/>
        <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
    </Style>

    <!-- 密码框样式 -->
    <Style x:Key="ModernPasswordBoxStyle" TargetType="PasswordBox" BasedOn="{StaticResource MaterialDesignOutlinedPasswordBox}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        <Setter Property="materialDesign:TextFieldAssist.HasOutlinedTextField" Value="True"/>
        <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
    </Style>

    <!-- 组合框样式 -->
    <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="materialDesign:HintAssist.FloatingScale" Value="0.75"/>
        <Setter Property="materialDesign:TextFieldAssist.HasOutlinedTextField" Value="True"/>
        <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
    </Style>

    <!-- 复选框样式 -->
    <Style x:Key="ModernCheckBoxStyle" TargetType="CheckBox" BasedOn="{StaticResource MaterialDesignCheckBox}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>

    <!-- 单选按钮样式 -->
    <Style x:Key="ModernRadioButtonStyle" TargetType="RadioButton" BasedOn="{StaticResource MaterialDesignRadioButton}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>

    <!-- 进度条样式 -->
    <Style x:Key="ModernProgressBarStyle" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignLinearProgressBar}">
        <Setter Property="Height" Value="8"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="materialDesign:TransitionAssist.DisableTransitions" Value="False"/>
    </Style>

    <!-- 列表视图样式 -->
    <Style x:Key="ModernListViewStyle" TargetType="ListView" BasedOn="{StaticResource MaterialDesignListView}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="materialDesign:ListViewAssist.ListViewItemPadding" Value="16,8"/>
    </Style>

    <!-- 数据网格样式 -->
    <Style x:Key="ModernDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="RowBackground" Value="Transparent"/>
        <Setter Property="AlternatingRowBackground" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="materialDesign:DataGridAssist.CellPadding" Value="16,8"/>
        <Setter Property="materialDesign:DataGridAssist.ColumnHeaderPadding" Value="16,12"/>
    </Style>

    <!-- 标签页样式 -->
    <Style x:Key="ModernTabControlStyle" TargetType="TabControl" BasedOn="{StaticResource MaterialDesignTabControl}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="materialDesign:ColorZoneAssist.Mode" Value="PrimaryMid"/>
    </Style>

    <!-- 菜单样式 -->
    <Style x:Key="ModernMenuStyle" TargetType="Menu" BasedOn="{StaticResource MaterialDesignMenu}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <!-- 工具栏样式 -->
    <Style x:Key="ModernToolBarStyle" TargetType="ToolBar" BasedOn="{StaticResource MaterialDesignToolBar}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
    </Style>

    <!-- 状态栏样式 -->
    <Style x:Key="ModernStatusBarStyle" TargetType="StatusBar">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="Height" Value="28"/>
    </Style>

    <!-- 分组框样式 -->
    <Style x:Key="ModernGroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource MaterialDesignGroupBox}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="materialDesign:ColorZoneAssist.Mode" Value="Standard"/>
    </Style>

    <!-- 展开器样式 -->
    <Style x:Key="ModernExpanderStyle" TargetType="Expander" BasedOn="{StaticResource MaterialDesignExpander}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="materialDesign:ExpanderAssist.HorizontalHeaderPadding" Value="16,8"/>
    </Style>

    <!-- 滑块样式 -->
    <Style x:Key="ModernSliderStyle" TargetType="Slider" BasedOn="{StaticResource MaterialDesignSlider}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Height" Value="24"/>
    </Style>

    <!-- 切换按钮样式 -->
    <Style x:Key="ModernToggleButtonStyle" TargetType="ToggleButton" BasedOn="{StaticResource MaterialDesignSwitchToggleButton}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="materialDesign:ToggleButtonAssist.SwitchTrackOnBackground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="materialDesign:ToggleButtonAssist.SwitchTrackOffBackground" Value="{StaticResource TextSecondaryBrush}"/>
    </Style>

    <!-- 工具提示样式 -->
    <Style x:Key="ModernToolTipStyle" TargetType="ToolTip" BasedOn="{StaticResource MaterialDesignToolTip}">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Background" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
    </Style>

    <!-- 对话框样式 -->
    <Style x:Key="ModernDialogStyle" TargetType="materialDesign:DialogHost">
        <Setter Property="DialogTheme" Value="Inherit"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth5"/>
    </Style>

    <!-- 浮动操作按钮样式 -->
    <Style x:Key="FloatingActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFloatingActionButton}">
        <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Width" Value="56"/>
        <Setter Property="Height" Value="56"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth3"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="28"/>
    </Style>

    <!-- 图标按钮样式 -->
    <Style x:Key="IconButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Margin" Value="2"/>
    </Style>

    <!-- 加载指示器样式 -->
    <Style x:Key="LoadingIndicatorStyle" TargetType="materialDesign:Card">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth3"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
    </Style>

</ResourceDictionary>
