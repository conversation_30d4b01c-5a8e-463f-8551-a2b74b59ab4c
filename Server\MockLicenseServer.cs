using System;
using System.Collections.Generic;
using System.Text.Json;
using CursorReset.Models;
using CursorReset.Services;

namespace CursorReset.Server
{
    /// <summary>
    /// 模拟许可证服务器 - 仅用于演示和测试
    /// 实际部署时应该使用真实的Web API服务器
    /// </summary>
    public class MockLicenseServer
    {
        private static readonly Dictionary<string, LicenseInfo> ValidLicenses = new()
        {
            // 预定义的测试许可证
            ["DEMO-1234-5678-9ABC"] = new LicenseInfo
            {
                LicenseKey = "DEMO-1234-5678-9ABC",
                UserEmail = "<EMAIL>",
                UserName = "Demo User",
                IssueDate = DateTime.Now.AddDays(-30),
                ExpiryDate = DateTime.Now.AddYears(1),
                Type = LicenseType.Professional,
                IsActivated = false,
                MaxActivations = 3,
                CurrentActivations = 0,
                ProductVersion = "1.0.0"
            },
            ["TEST-ABCD-EFGH-IJKL"] = new LicenseInfo
            {
                LicenseKey = "TEST-ABCD-EFGH-IJKL",
                UserEmail = "<EMAIL>",
                UserName = "Test User",
                IssueDate = DateTime.Now.AddDays(-10),
                ExpiryDate = DateTime.Now.AddMonths(6),
                Type = LicenseType.Personal,
                IsActivated = false,
                MaxActivations = 1,
                CurrentActivations = 0,
                ProductVersion = "1.0.0"
            },
            ["ENTERPRISE-2024-FULL"] = new LicenseInfo
            {
                LicenseKey = "ENTERPRISE-2024-FULL",
                UserEmail = "<EMAIL>",
                UserName = "Enterprise Admin",
                IssueDate = DateTime.Now.AddDays(-60),
                ExpiryDate = DateTime.Now.AddYears(3),
                Type = LicenseType.Enterprise,
                IsActivated = false,
                MaxActivations = 100,
                CurrentActivations = 0,
                ProductVersion = "1.0.0"
            }
        };

        public static ActivationResponse ProcessActivation(ActivationRequest request)
        {
            try
            {
                // 验证许可证密钥格式
                if (string.IsNullOrWhiteSpace(request.LicenseKey))
                {
                    return new ActivationResponse
                    {
                        Success = false,
                        Message = "许可证密钥不能为空",
                        ErrorCode = "EMPTY_KEY"
                    };
                }

                // 查找许可证
                if (!ValidLicenses.TryGetValue(request.LicenseKey, out var license))
                {
                    return new ActivationResponse
                    {
                        Success = false,
                        Message = "无效的许可证密钥",
                        ErrorCode = "INVALID_KEY"
                    };
                }

                // 检查许可证是否过期
                if (DateTime.Now > license.ExpiryDate)
                {
                    return new ActivationResponse
                    {
                        Success = false,
                        Message = "许可证已过期",
                        ErrorCode = "EXPIRED"
                    };
                }

                // 检查激活次数限制
                if (license.CurrentActivations >= license.MaxActivations)
                {
                    return new ActivationResponse
                    {
                        Success = false,
                        Message = "已达到最大激活次数限制",
                        ErrorCode = "MAX_ACTIVATIONS"
                    };
                }

                // 创建激活的许可证副本
                var activatedLicense = new LicenseInfo
                {
                    LicenseKey = license.LicenseKey,
                    UserEmail = request.UserEmail,
                    UserName = request.UserEmail.Split('@')[0],
                    IssueDate = license.IssueDate,
                    ExpiryDate = license.ExpiryDate,
                    Type = license.Type,
                    MachineId = request.MachineId,
                    IsActivated = true,
                    MaxActivations = license.MaxActivations,
                    CurrentActivations = license.CurrentActivations + 1,
                    ProductVersion = request.ProductVersion
                };

                // 生成签名
                var dataToSign = $"{activatedLicense.LicenseKey}{activatedLicense.UserEmail}{activatedLicense.ExpiryDate:yyyy-MM-dd}{activatedLicense.MachineId}";
                activatedLicense.Signature = CryptographyService.GenerateSignature(dataToSign, "ServerPrivateKey");

                // 更新服务器端的激活计数
                license.CurrentActivations++;

                return new ActivationResponse
                {
                    Success = true,
                    Message = "许可证激活成功",
                    License = activatedLicense
                };
            }
            catch (Exception ex)
            {
                return new ActivationResponse
                {
                    Success = false,
                    Message = $"服务器内部错误: {ex.Message}",
                    ErrorCode = "SERVER_ERROR"
                };
            }
        }

        public static ActivationResponse ProcessValidation(string licenseKey, string machineId)
        {
            try
            {
                if (!ValidLicenses.TryGetValue(licenseKey, out var license))
                {
                    return new ActivationResponse
                    {
                        Success = false,
                        Message = "许可证不存在",
                        ErrorCode = "NOT_FOUND"
                    };
                }

                if (DateTime.Now > license.ExpiryDate)
                {
                    return new ActivationResponse
                    {
                        Success = false,
                        Message = "许可证已过期",
                        ErrorCode = "EXPIRED"
                    };
                }

                return new ActivationResponse
                {
                    Success = true,
                    Message = "许可证验证成功",
                    License = license
                };
            }
            catch (Exception ex)
            {
                return new ActivationResponse
                {
                    Success = false,
                    Message = $"验证失败: {ex.Message}",
                    ErrorCode = "VALIDATION_ERROR"
                };
            }
        }

        public static void PrintTestLicenses()
        {
            Console.WriteLine("=== 测试许可证密钥 ===");
            Console.WriteLine();
            
            foreach (var kvp in ValidLicenses)
            {
                var license = kvp.Value;
                Console.WriteLine($"许可证密钥: {license.LicenseKey}");
                Console.WriteLine($"类型: {GetLicenseTypeText(license.Type)}");
                Console.WriteLine($"最大激活数: {license.MaxActivations}");
                Console.WriteLine($"当前激活数: {license.CurrentActivations}");
                Console.WriteLine($"到期日期: {license.ExpiryDate:yyyy-MM-dd}");
                Console.WriteLine($"状态: {(DateTime.Now <= license.ExpiryDate ? "有效" : "已过期")}");
                Console.WriteLine();
            }
            
            Console.WriteLine("💡 提示：复制上述任意许可证密钥进行测试激活");
        }

        private static string GetLicenseTypeText(LicenseType type)
        {
            return type switch
            {
                LicenseType.Trial => "试用版",
                LicenseType.Personal => "个人版",
                LicenseType.Professional => "专业版",
                LicenseType.Enterprise => "企业版",
                _ => "未知"
            };
        }

        /// <summary>
        /// 生成新的测试许可证
        /// </summary>
        public static string GenerateTestLicense(LicenseType type, int validDays = 365)
        {
            var licenseKey = CryptographyService.GenerateLicenseKey();
            
            var license = new LicenseInfo
            {
                LicenseKey = licenseKey,
                UserEmail = "<EMAIL>",
                UserName = "Generated User",
                IssueDate = DateTime.Now,
                ExpiryDate = DateTime.Now.AddDays(validDays),
                Type = type,
                IsActivated = false,
                MaxActivations = type == LicenseType.Enterprise ? 100 : (type == LicenseType.Professional ? 3 : 1),
                CurrentActivations = 0,
                ProductVersion = "1.0.0"
            };

            ValidLicenses[licenseKey] = license;
            
            return licenseKey;
        }
    }
}
